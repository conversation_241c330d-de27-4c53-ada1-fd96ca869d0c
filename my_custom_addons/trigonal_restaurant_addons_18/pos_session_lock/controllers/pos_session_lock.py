from odoo import http
import logging

_logger = logging.getLogger(__name__)

class PosSessionLockController(http.Controller):
    """Controller for POS Session Lock functionality."""
    
    @http.route('/pos/session_lock/heartbeat', type='json', auth='user', methods=['POST'])
    def heartbeat(self, session_id=None, device_id=None, **kwargs):
        """Handle heartbeat requests from devices."""
        _logger.debug("[SessionLock] Heartbeat request: session_id=%s, device_id=%s", session_id, device_id)
        try:
            if not session_id or not device_id:
                _logger.error("[SessionLock] Missing parameters for heartbeat: session_id=%s, device_id=%s", session_id, device_id)
                return {'status': 'error', 'message': 'Missing session_id or device_id'}
            
            result = http.request.env['pos.session.lock'].sudo().heartbeat(session_id, device_id)
            _logger.debug("[SessionLock] Heartbeat result for session %s, device %s: %s", session_id, device_id, result)
            return result
        except Exception as e:
            _logger.error("[SessionLock] Heartbeat error for session %s, device %s: %s", session_id, device_id, str(e))
            return {'status': 'error', 'message': str(e)}

    @http.route('/pos/session_lock/check', type='json', auth='user', methods=['POST'])
    def check_session_lock(self, session_id=None, device_id=None, **kwargs):
        """Check if session is locked by other devices."""
        _logger.debug("[SessionLock] Check lock request: session_id=%s, device_id=%s", session_id, device_id)
        try:
            if not session_id or not device_id:
                _logger.error("[SessionLock] Missing parameters for check: session_id=%s, device_id=%s", session_id, device_id)
                return {'status': 'error', 'message': 'Missing session_id or device_id'}
            
            result = http.request.env['pos.session.lock'].sudo().check_session_lock(session_id, device_id)
            _logger.debug("[SessionLock] Check lock result for session %s, device %s: %s", session_id, device_id, result)
            return result
        except Exception as e:
            _logger.error("[SessionLock] Check lock error for session %s, device %s: %s", session_id, device_id, str(e))
            return {'status': 'error', 'message': str(e)}

    @http.route('/pos/session_lock/can_close', type='json', auth='user', methods=['POST'])
    def can_close_session(self, session_id=None, device_id=None, **kwargs):
        """Check if a device can close the session."""
        _logger.debug("[SessionLock] Can close request: session_id=%s, device_id=%s", session_id, device_id)
        try:
            if not session_id or not device_id:
                _logger.error("[SessionLock] Missing parameters for can_close: session_id=%s, device_id=%s", session_id, device_id)
                return {'status': 'error', 'message': 'Missing session_id or device_id'}
            
            result = http.request.env['pos.session.lock'].sudo().can_close_session(session_id, device_id)
            _logger.debug("[SessionLock] Can close result for session %s, device %s: %s", session_id, device_id, result)
            return result
        except Exception as e:
            _logger.error("[SessionLock] Can close error for session %s, device %s: %s", session_id, device_id, str(e))
            return {'status': 'error', 'message': str(e)}
