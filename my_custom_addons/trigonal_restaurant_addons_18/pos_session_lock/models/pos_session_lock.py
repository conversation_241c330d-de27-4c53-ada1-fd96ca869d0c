from odoo import models, fields, api
import logging
from datetime import datetime, timedelta

_logger = logging.getLogger(__name__)

class PosSessionLock(models.Model):
    _name = "pos.session.lock"
    _description = "POS Session Lock"

    session_id = fields.Many2one('pos.session', string='Session', required=True, index=True,
                                 domain="[('state', '=', 'opened')]")
    device_id = fields.Char(string='Device ID', required=True, index=True)
    is_active = fields.Boolean(string='Is Active', default=True)
    last_heartbeat = fields.Datetime(string='Last Heartbeat', default=fields.Datetime.now)
    user_id = fields.Many2one('res.users', string='User', required=True)

    @api.model
    def create_session_lock(self, session_id, device_id, user_id):
        """Create or update a session lock for a device."""
        _logger.info("[SessionLock] Creating/updating lock for session %s, device %s, user %s",
                     session_id, device_id, user_id)

        if not all([session_id, device_id, user_id]):
            _logger.error("[SessionLock] Invalid parameters: session_id=%s, device_id=%s, user_id=%s",
                         session_id, device_id, user_id)
            return {'status': 'error', 'message': 'Missing required parameters'}

        # Check if session exists and is open
        session = self.env['pos.session'].search([('id', '=', session_id), ('state', '=', 'opened')], limit=1)
        if not session:
            _logger.error("[SessionLock] Session %s does not exist or is not open", session_id)
            return {'status': 'error', 'message': 'Session does not exist or is not open'}

        # Get user information for logging
        user = self.env['res.users'].browse(user_id)
        user_name = user.name if user else f"User {user_id}"

        # Check existing devices in this session for logging
        existing_locks = self.search([('session_id', '=', session_id), ('is_active', '=', True)])
        existing_devices = []
        for existing_lock in existing_locks:
            if existing_lock.device_id != device_id:  # Don't include current device
                existing_user = existing_lock.user_id.name if existing_lock.user_id else "Unknown"
                existing_devices.append(f"{existing_user} ({existing_lock.device_id})")

        # Find or create lock for this device
        lock = self.search([('session_id', '=', session_id), ('device_id', '=', device_id)], limit=1)
        if lock:
            lock.write({
                'is_active': True,
                'last_heartbeat': datetime.now(),
                'user_id': user_id
            })
            _logger.info("[SessionLock] ✅ DEVICE RECONNECTED: %s (%s) reconnected to session %s",
                        user_name, device_id, session_id)
        else:
            lock = self.create({
                'session_id': session_id,
                'device_id': device_id,
                'user_id': user_id,
                'is_active': True,
                'last_heartbeat': datetime.now()
            })
            _logger.info("[SessionLock] 🆕 NEW DEVICE JOINED: %s (%s) opened session %s",
                        user_name, device_id, session_id)

        # Log existing devices if any
        if existing_devices:
            _logger.info("[SessionLock] 📱 OTHER ACTIVE DEVICES in session %s: %s",
                        session_id, ", ".join(existing_devices))
        else:
            _logger.info("[SessionLock] 📱 SINGLE DEVICE: %s (%s) is the only device in session %s",
                        user_name, device_id, session_id)

        # Get current active device count and list
        active_count = self.get_active_device_count(session_id)
        active_devices = self.get_active_devices(session_id)

        return {
            'status': 'success',
            'active_devices': active_count,
            'device_list': active_devices
        }

    @api.model
    def heartbeat(self, session_id, device_id):
        """Send heartbeat for a device and return current session status."""
        _logger.info("[SessionLock] Heartbeat for session %s, device %s", session_id, device_id)

        if not all([session_id, device_id]):
            _logger.error("[SessionLock] Invalid heartbeat parameters: session_id=%s, device_id=%s",
                         session_id, device_id)
            return {'status': 'error', 'message': 'Missing required parameters'}

        # Clean up stale locks first
        self.cleanup_stale_locks()

        # Find the lock for this device
        lock = self.search([('session_id', '=', session_id), ('device_id', '=', device_id)], limit=1)
        if lock:
            lock.write({
                'is_active': True,
                'last_heartbeat': datetime.now()
            })
            _logger.info("[SessionLock] Updated heartbeat for session %s, device %s", session_id, device_id)
        else:
            _logger.warning("[SessionLock] No lock found for heartbeat session %s, device %s", session_id, device_id)
            return {'status': 'inactive', 'message': 'Device not registered in session'}

        # Get current active device count and list
        active_count = self.get_active_device_count(session_id)
        active_devices = self.get_active_devices(session_id)

        return {
            'status': 'active',
            'active_devices': active_count,
            'device_list': active_devices
        }

    @api.model
    def get_active_device_count(self, session_id):
        """Return the count of active devices for the given session."""
        count = self.search_count([('session_id', '=', session_id), ('is_active', '=', True)])
        _logger.info("[SessionLock] Active device count for session %s: %d", session_id, count)
        return count

    @api.model
    def get_active_devices(self, session_id):
        """Return list of active devices with user info for the given session."""
        locks = self.search([('session_id', '=', session_id), ('is_active', '=', True)])
        device_list = []
        for lock in locks:
            device_list.append({
                'device_id': lock.device_id,
                'user_name': lock.user_id.name,
                'last_heartbeat': lock.last_heartbeat.isoformat() if lock.last_heartbeat else None
            })
        _logger.info("[SessionLock] Active devices for session %s: %s", session_id, device_list)
        return device_list

    @api.model
    def can_close_session(self, session_id, device_id):
        """Check if a device can close the session."""
        _logger.info("[SessionLock] 🔒 CLOSE CHECK: Device %s wants to close session %s", device_id, session_id)

        if not all([session_id, device_id]):
            return {'can_close': False, 'message': 'Missing required parameters'}

        # Clean up stale locks first
        stale_count = self.cleanup_stale_locks()
        if stale_count > 0:
            _logger.info("[SessionLock] 🧹 Cleaned up %d stale device locks", stale_count)

        # Get active device count and details
        active_count = self.get_active_device_count(session_id)
        active_devices = self.get_active_devices(session_id)

        _logger.info("[SessionLock] 📊 ACTIVE DEVICES in session %s: %d total", session_id, active_count)

        # Log all active devices for debugging
        for device in active_devices:
            _logger.info("[SessionLock] 📱 Active: %s (%s) - last seen: %s",
                        device['user_name'], device['device_id'], device['last_heartbeat'])

        # Only prevent closure if there are multiple active devices
        if active_count > 1:
            device_list = [f"{d['user_name']} ({d['device_id']})" for d in active_devices]
            message = f"❌ Cannot close session! {active_count} devices are still active: {', '.join(device_list)}"
            _logger.warning("[SessionLock] %s", message)
            return {'can_close': False, 'message': message}
        elif active_count == 1:
            # Check if the only active device is the one requesting closure
            only_device = active_devices[0] if active_devices else None
            if only_device and only_device['device_id'] == device_id:
                _logger.info("[SessionLock] ✅ CLOSURE ALLOWED: Only device %s (%s) is active in session %s",
                           only_device['user_name'], device_id, session_id)
                return {'can_close': True, 'message': 'Session can be closed - you are the only active device'}
            else:
                _logger.warning("[SessionLock] ⚠️ CLOSURE BLOCKED: Device %s is not the active device in session %s",
                              device_id, session_id)
                return {'can_close': False, 'message': 'Another device is active in this session'}
        else:
            # No active devices found
            _logger.info("[SessionLock] ✅ CLOSURE ALLOWED: No active devices found in session %s", session_id)
            return {'can_close': True, 'message': 'Session can be closed - no active devices'}

    @api.model
    def release_session_lock(self, session_id, device_id):
        """Release session lock for a device."""
        _logger.info("[SessionLock] Releasing lock for session %s, device %s", session_id, device_id)

        if not all([session_id, device_id]):
            return {'status': 'error', 'message': 'Missing required parameters'}

        lock = self.search([('session_id', '=', session_id), ('device_id', '=', device_id)], limit=1)
        if lock:
            lock.write({'is_active': False})
            _logger.info("[SessionLock] Released lock for session %s, device %s", session_id, device_id)
            return {'status': 'success', 'message': 'Lock released'}
        else:
            _logger.warning("[SessionLock] No lock found to release for session %s, device %s", session_id, device_id)
            return {'status': 'no_lock', 'message': 'No active lock found'}

    @api.model
    def cleanup_stale_locks(self):
        """Clean up stale locks (older than 15 seconds for faster updates)."""
        timeout = datetime.now() - timedelta(seconds=15)
        stale_locks = self.search([('last_heartbeat', '<', timeout), ('is_active', '=', True)])
        if stale_locks:
            stale_locks.write({'is_active': False})
            _logger.info("[SessionLock] Cleaned up %d stale locks", len(stale_locks))
        return len(stale_locks)

    @api.model
    def _load_pos_data_domain(self, data):
        """Define domain for loading POS data - only load active locks for current session."""
        session_id = data.get('pos.session', {}).get('data', [{}])[0].get('id')
        if session_id:
            return [('session_id', '=', session_id), ('is_active', '=', True)]
        return [('id', '=', False)]  # Load nothing if no session

    @api.model
    def _load_pos_data_fields(self, config_id):
        """Define fields to load for POS."""
        return ['session_id', 'device_id', 'is_active', 'last_heartbeat', 'user_id']

    @api.model
    def _load_pos_data(self, data):
        """Load POS data for session locks."""
        domain = self._load_pos_data_domain(data)
        fields = self._load_pos_data_fields(data.get('pos.config', {}).get('data', [{}])[0].get('id'))
        records = self.search_read(domain, fields)
        return {
            'data': records,
            'fields': fields
        }
