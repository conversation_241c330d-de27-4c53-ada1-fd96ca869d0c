from odoo import models, fields, api
import logging
from datetime import datetime, timedelta

_logger = logging.getLogger(__name__)

class PosSessionLock(models.Model):
    _name = "pos.session.lock"
    _description = "POS Session Lock"

    session_id = fields.Many2one('pos.session', string='Session', required=True, index=True,
                                 domain="[('state', '=', 'opened')]")
    device_id = fields.Char(string='Device ID', required=True, index=True)
    is_active = fields.Boolean(string='Is Active', default=True)
    last_heartbeat = fields.Datetime(string='Last Heartbeat', default=fields.Datetime.now)
    user_id = fields.Many2one('res.users', string='User', required=True)

    @api.model
    def create_session_lock(self, session_id, device_id, user_id):
        """Create or update a session lock for a device."""
        _logger.info("[SessionLock] Creating/updating lock for session %s, device %s, user %s",
                     session_id, device_id, user_id)

        if not all([session_id, device_id, user_id]):
            _logger.error("[SessionLock] Invalid parameters: session_id=%s, device_id=%s, user_id=%s",
                         session_id, device_id, user_id)
            return {'status': 'error', 'message': 'Missing required parameters'}

        # Check if session exists and is open
        session = self.env['pos.session'].search([('id', '=', session_id), ('state', '=', 'opened')], limit=1)
        if not session:
            _logger.error("[SessionLock] Session %s does not exist or is not open", session_id)
            return {'status': 'error', 'message': 'Session does not exist or is not open'}

        # Find or create lock for this device
        lock = self.search([('session_id', '=', session_id), ('device_id', '=', device_id)], limit=1)
        if lock:
            lock.write({
                'is_active': True,
                'last_heartbeat': datetime.now(),
                'user_id': user_id
            })
            _logger.info("[SessionLock] Updated existing lock for session %s, device %s", session_id, device_id)
        else:
            lock = self.create({
                'session_id': session_id,
                'device_id': device_id,
                'user_id': user_id,
                'is_active': True,
                'last_heartbeat': datetime.now()
            })
            _logger.info("[SessionLock] Created new lock for session %s, device %s", session_id, device_id)

        # Get current active device count and list
        active_count = self.get_active_device_count(session_id)
        active_devices = self.get_active_devices(session_id)

        return {
            'status': 'success',
            'active_devices': active_count,
            'device_list': active_devices
        }

    @api.model
    def heartbeat(self, session_id, device_id):
        """Send heartbeat for a device and return current session status."""
        _logger.info("[SessionLock] Heartbeat for session %s, device %s", session_id, device_id)

        if not all([session_id, device_id]):
            _logger.error("[SessionLock] Invalid heartbeat parameters: session_id=%s, device_id=%s",
                         session_id, device_id)
            return {'status': 'error', 'message': 'Missing required parameters'}

        # Clean up stale locks first
        self.cleanup_stale_locks()

        # Find the lock for this device
        lock = self.search([('session_id', '=', session_id), ('device_id', '=', device_id)], limit=1)
        if lock:
            lock.write({
                'is_active': True,
                'last_heartbeat': datetime.now()
            })
            _logger.info("[SessionLock] Updated heartbeat for session %s, device %s", session_id, device_id)
        else:
            _logger.warning("[SessionLock] No lock found for heartbeat session %s, device %s", session_id, device_id)
            return {'status': 'inactive', 'message': 'Device not registered in session'}

        # Get current active device count and list
        active_count = self.get_active_device_count(session_id)
        active_devices = self.get_active_devices(session_id)

        return {
            'status': 'active',
            'active_devices': active_count,
            'device_list': active_devices
        }

    @api.model
    def get_active_device_count(self, session_id):
        """Return the count of active devices for the given session."""
        count = self.search_count([('session_id', '=', session_id), ('is_active', '=', True)])
        _logger.info("[SessionLock] Active device count for session %s: %d", session_id, count)
        return count

    @api.model
    def get_active_devices(self, session_id):
        """Return list of active devices with user info for the given session."""
        locks = self.search([('session_id', '=', session_id), ('is_active', '=', True)])
        device_list = []
        for lock in locks:
            device_list.append({
                'device_id': lock.device_id,
                'user_name': lock.user_id.name,
                'last_heartbeat': lock.last_heartbeat.isoformat() if lock.last_heartbeat else None
            })
        _logger.info("[SessionLock] Active devices for session %s: %s", session_id, device_list)
        return device_list

    @api.model
    def can_close_session(self, session_id, device_id):
        """Check if a device can close the session."""
        _logger.info("[SessionLock] Checking if device %s can close session %s", device_id, session_id)

        if not all([session_id, device_id]):
            return {'can_close': False, 'message': 'Missing required parameters'}

        # Clean up stale locks first
        self.cleanup_stale_locks()

        # Get active device count
        active_count = self.get_active_device_count(session_id)
        active_devices = self.get_active_devices(session_id)

        if active_count > 1:
            device_list = [f"{d['user_name']} ({d['device_id']})" for d in active_devices]
            message = f"Cannot close session. {active_count} devices are active: {', '.join(device_list)}"
            _logger.warning("[SessionLock] %s", message)
            return {'can_close': False, 'message': message}

        _logger.info("[SessionLock] Device %s can close session %s", device_id, session_id)
        return {'can_close': True, 'message': 'Session can be closed'}

    @api.model
    def release_session_lock(self, session_id, device_id):
        """Release session lock for a device."""
        _logger.info("[SessionLock] Releasing lock for session %s, device %s", session_id, device_id)

        if not all([session_id, device_id]):
            return {'status': 'error', 'message': 'Missing required parameters'}

        lock = self.search([('session_id', '=', session_id), ('device_id', '=', device_id)], limit=1)
        if lock:
            lock.write({'is_active': False})
            _logger.info("[SessionLock] Released lock for session %s, device %s", session_id, device_id)
            return {'status': 'success', 'message': 'Lock released'}
        else:
            _logger.warning("[SessionLock] No lock found to release for session %s, device %s", session_id, device_id)
            return {'status': 'no_lock', 'message': 'No active lock found'}

    @api.model
    def cleanup_stale_locks(self):
        """Clean up stale locks (older than 2 minutes)."""
        timeout = datetime.now() - timedelta(minutes=2)
        stale_locks = self.search([('last_heartbeat', '<', timeout), ('is_active', '=', True)])
        if stale_locks:
            stale_locks.write({'is_active': False})
            _logger.info("[SessionLock] Cleaned up %d stale locks", len(stale_locks))
        return len(stale_locks)

    @api.model
    def _load_pos_data_domain(self, data):
        """Define domain for loading POS data - only load active locks for current session."""
        session_id = data.get('pos.session', {}).get('data', [{}])[0].get('id')
        if session_id:
            return [('session_id', '=', session_id), ('is_active', '=', True)]
        return [('id', '=', False)]  # Load nothing if no session

    @api.model
    def _load_pos_data_fields(self, config_id):
        """Define fields to load for POS."""
        return ['session_id', 'device_id', 'is_active', 'last_heartbeat', 'user_id']

    @api.model
    def _load_pos_data(self, data):
        """Load POS data for session locks."""
        domain = self._load_pos_data_domain(data)
        fields = self._load_pos_data_fields(data.get('pos.config', {}).get('data', [{}])[0].get('id'))
        records = self.search_read(domain, fields)
        return {
            'data': records,
            'fields': fields
        }
