from odoo import models, fields, api
import logging
from datetime import datetime, timedelta

_logger = logging.getLogger(__name__)

class PosSessionLock(models.Model):
    _name = "pos.session.lock"
    _description = "POS Session Lock"

    session_id = fields.Many2one('pos.session', string='Session', required=True, index=True,
                                 domain="[('state', '=', 'opened')]")
    device_id = fields.Char(string='Device ID', required=True, index=True)
    is_active = fields.Boolean(string='Is Active', default=True)
    last_heartbeat = fields.Datetime(string='Last Heartbeat', default=fields.Datetime.now)
    user_id = fields.Many2one('res.users', string='User', required=True)

    @api.model
    def create_session_lock(self, session_id, device_id, user_id):
        """Create or update a session lock for a device."""
        _logger.info("[SessionLock] Creating/updating lock for session %s, device %s, user %s",
                     session_id, device_id, user_id)

        if not all([session_id, device_id, user_id]):
            _logger.error("[SessionLock] Invalid parameters: session_id=%s, device_id=%s, user_id=%s",
                         session_id, device_id, user_id)
            return {'status': 'error', 'message': 'Missing required parameters'}

        # Check if session exists and is open
        session = self.env['pos.session'].search([('id', '=', session_id), ('state', '=', 'opened')], limit=1)
        if not session:
            _logger.error("[SessionLock] Session %s does not exist or is not open", session_id)
            return {'status': 'error', 'message': 'Session does not exist or is not open'}

        # Find or create lock for this device
        lock = self.search([('session_id', '=', session_id), ('device_id', '=', device_id)], limit=1)
        if lock:
            lock.write({
                'is_active': True,
                'last_heartbeat': datetime.now(),
                'user_id': user_id
            })
            _logger.info("[SessionLock] Updated existing lock for session %s, device %s", session_id, device_id)
        else:
            lock = self.create({
                'session_id': session_id,
                'device_id': device_id,
                'user_id': user_id,
                'is_active': True,
                'last_heartbeat': datetime.now()
            })
            _logger.info("[SessionLock] Created new lock for session %s, device %s", session_id, device_id)

        # Get current active device count and list
        active_count = self.get_active_device_count(session_id)
        active_devices = self.get_active_devices(session_id)

        return {
            'status': 'success',
            'active_devices': active_count,
            'device_list': active_devices
        }

    @api.model
    def heartbeat(self, session_id, device_id):
        """Send heartbeat for a device and return current session status."""
        _logger.info("[SessionLock] Heartbeat for session %s, device %s", session_id, device_id)

        if not all([session_id, device_id]):
            _logger.error("[SessionLock] Invalid heartbeat parameters: session_id=%s, device_id=%s",
                         session_id, device_id)
            return {'status': 'error', 'message': 'Missing required parameters'}

        # Clean up stale locks first
        self.cleanup_stale_locks()

        # Find the lock for this device
        lock = self.search([('session_id', '=', session_id), ('device_id', '=', device_id)], limit=1)
        if lock:
            lock.write({
                'is_active': True,
                'last_heartbeat': datetime.now()
            })
            _logger.info("[SessionLock] Updated heartbeat for session %s, device %s", session_id, device_id)
        else:
            _logger.warning("[SessionLock] No lock found for heartbeat session %s, device %s", session_id, device_id)
            return {'status': 'inactive', 'message': 'Device not registered in session'}

        # Get current active device count and list
        active_count = self.get_active_device_count(session_id)
        active_devices = self.get_active_devices(session_id)

        return {
            'status': 'active',
            'active_devices': active_count,
            'device_list': active_devices
        }

    @api.model
    def get_active_device_count(self, session_id):
        """Return the count of active devices for the given session."""
        count = self.search_count([('session_id', '=', session_id), ('is_active', '=', True)])
        _logger.info("[SessionLock] Active device count for session %s: %d", session_id, count)
        return count

    @api.model
    def get_active_devices(self, session_id):
        """Return list of active devices with user info for the given session."""
        locks = self.search([('session_id', '=', session_id), ('is_active', '=', True)])
        device_list = []
        for lock in locks:
            device_list.append({
                'device_id': lock.device_id,
                'user_name': lock.user_id.name,
                'last_heartbeat': lock.last_heartbeat.isoformat() if lock.last_heartbeat else None
            })
        _logger.info("[SessionLock] Active devices for session %s: %s", session_id, device_list)
        return device_list

    @api.model
    def can_close_session(self, session_id, device_id):
        """Check if a device can close the session."""
        _logger.info("[SessionLock] Checking if device %s can close session %s", device_id, session_id)

        if not all([session_id, device_id]):
            return {'can_close': False, 'message': 'Missing required parameters'}

        # Clean up stale locks first
        self.cleanup_stale_locks()

        # Get active device count
        active_count = self.get_active_device_count(session_id)
        active_devices = self.get_active_devices(session_id)

        if active_count > 1:
            device_list = [f"{d['user_name']} ({d['device_id']})" for d in active_devices]
            message = f"Cannot close session. {active_count} devices are active: {', '.join(device_list)}"
            _logger.warning("[SessionLock] %s", message)
            return {'can_close': False, 'message': message}

        _logger.info("[SessionLock] Device %s can close session %s", device_id, session_id)
        return {'can_close': True, 'message': 'Session can be closed'}

    @api.model
    def release_session_lock(self, session_id, device_id):
        """Release session lock for a device."""
        _logger.info("[SessionLock] Releasing lock for session %s, device %s", session_id, device_id)

        if not all([session_id, device_id]):
            return {'status': 'error', 'message': 'Missing required parameters'}

        lock = self.search([('session_id', '=', session_id), ('device_id', '=', device_id)], limit=1)
        if lock:
            lock.write({'is_active': False})
            _logger.info("[SessionLock] Released lock for session %s, device %s", session_id, device_id)
            return {'status': 'success', 'message': 'Lock released'}
        else:
            _logger.warning("[SessionLock] No lock found to release for session %s, device %s", session_id, device_id)
            return {'status': 'no_lock', 'message': 'No active lock found'}

    @api.model
    def cleanup_stale_locks(self):
        """Clean up stale locks (older than 2 minutes)."""
        timeout = datetime.now() - timedelta(minutes=2)
        stale_locks = self.search([('last_heartbeat', '<', timeout), ('is_active', '=', True)])
        if stale_locks:
            stale_locks.write({'is_active': False})
            _logger.info("[SessionLock] Cleaned up %d stale locks", len(stale_locks))
        return len(stale_locks)

#     session_id = fields.Many2one('pos.session', string='Session', required=True, index=True,
#                                domain="[('state', '=', 'opened')]")
#     device_id = fields.Char(string='Device ID', required=True)
#     is_active = fields.Boolean(string='Is Active', default=True)
#     lock_time = fields.Datetime(string='Lock Time', default=fields.Datetime.now)
#     user_id = fields.Many2one('res.users', string='User', required=True)

#     @api.model
#     def create_session_lock(self, session_id, device_id, user_id):
#         _logger.info("[SessionLock] Creating lock for session %s, device %s", session_id, device_id)
#         existing_lock = self.search([('session_id', '=', session_id), ('is_active', '=', True)], limit=1)
#         if existing_lock and existing_lock.device_id != device_id:
#             _logger.warning("[SessionLock] Session %s locked by device %s", session_id, existing_lock.device_id)
#             return {
#                 'status': 'locked',
#                 'device_id': existing_lock.device_id,
#                 'user_name': existing_lock.user_id.name
#             }
#         if existing_lock:
#             existing_lock.write({'lock_time': datetime.now(), 'user_id': user_id})
#         else:
#             self.create({
#                 'session_id': session_id,
#                 'device_id': device_id,
#                 'user_id': user_id,
#                 'lock_time': datetime.now()
#             })
#         return {'status': 'success'}

#     @api.model
#     def check_session_lock(self, session_id, device_id):
#         lock = self.search([('session_id', '=', session_id), ('is_active', '=', True)], limit=1)
#         if lock and lock.device_id != device_id:
#             return {
#                 'status': 'locked',
#                 'device_id': lock.device_id,
#                 'user_name': lock.user_id.name
#             }
#         return {'status': 'free'}

#     @api.model
#     def release_session_lock(self, session_id, device_id):
#         lock = self.search([('session_id', '=', session_id), ('is_active', '=', True)], limit=1)
#         if not lock:
#             return {'status': 'no_lock'}
#         if lock.device_id != device_id:
#             _logger.warning("[SessionLock] Device %s cannot release lock for session %s", device_id, session_id)
#             return {'status': 'error', 'message': 'Device mismatch'}
#         lock.write({'is_active': False})
#         _logger.info("[SessionLock] Lock released for session %s by device %s", session_id, device_id)
#         return {'status': 'success'}

#     @api.model
#     def cleanup_stale_locks(self):
#         timeout = datetime.now() - timedelta(minutes=5)
#         stale_locks = self.search([('lock_time', '<', timeout), ('is_active', '=', True)])
#         if stale_locks:
#             _logger.info("[SessionLock] Cleaning %d stale locks", len(stale_locks))
#             stale_locks.write({'is_active': False})

#     @api.model
#     def long_poll_session_status(self, session_id, device_id):
#         try:
#             self.cleanup_stale_locks()
#             lock = self.search([('session_id', '=', session_id), ('is_active', '=', True)], limit=1)
#             if not lock or lock.device_id == device_id:
#                 return {'status': 'free'}
#             timeout = datetime.now() + timedelta(seconds=30)
#             while datetime.now() < timeout:
#                 self.env.cr.commit()  # Ensure database state is updated
#                 lock = self.search([('session_id', '=', session_id), ('is_active', '=', True)], limit=1)
#                 if not lock or lock.device_id == device_id:
#                     return {'status': 'free'}
#                 if lock and lock.device_id != device_id:
#                     return {
#                         'status': 'locked',
#                         'device_id': lock.device_id,
#                         'user_name': lock.user_id.name
#                     }
#                 time.sleep(1)
#             return {'status': 'timeout'}
#         except Exception as e:
#             _logger.error("[SessionLock] Long poll error for session %s: %s", session_id, str(e))
#             return {'status': 'error', 'message': 'Polling failed, please try again'}


# from odoo import fields, models, api
# import logging
# from datetime import datetime, timedelta
# import time

# _logger = logging.getLogger(__name__)

# class PosSessionLock(models.Model):
#     _name = "pos.session.lock"
#     _description = "POS Session Lock"

#     session_id = fields.Many2one('pos.session', string='Session', required=True, index=True,
#                                domain="[('state', '=', 'opened')]")
#     device_id = fields.Char(string='Device ID', required=True)
#     is_active = fields.Boolean(string='Is Active', default=True)
#     lock_time = fields.Datetime(string='Lock Time', default=fields.Datetime.now)
#     user_id = fields.Many2one('res.users', string='User', required=True)

#     @api.model
#     def create_session_lock(self, session_id, device_id, user_id):
#         _logger.info("[SessionLock] Creating lock for session %s, device %s", session_id, device_id)
#         existing_lock = self.search([('session_id', '=', session_id), ('is_active', '=', True)], limit=1)
#         if existing_lock and existing_lock.device_id != device_id:
#             _logger.warning("[SessionLock] Session %s locked by device %s", session_id, existing_lock.device_id)
#             return {
#                 'status': 'locked',
#                 'device_id': existing_lock.device_id,
#                 'user_name': existing_lock.user_id.name
#             }
#         if existing_lock:
#             existing_lock.write({'lock_time': datetime.now(), 'user_id': user_id})
#         else:
#             self.create({
#                 'session_id': session_id,
#                 'device_id': device_id,
#                 'user_id': user_id,
#                 'lock_time': datetime.now()
#             })
#         return {'status': 'success'}

#     @api.model
#     def check_session_lock(self, session_id, device_id):
#         lock = self.search([('session_id', '=', session_id), ('is_active', '=', True)], limit=1)
#         if lock and lock.device_id != device_id:
#             return {
#                 'status': 'locked',
#                 'device_id': lock.device_id,
#                 'user_name': lock.user_id.name
#             }
#         return {'status': 'free'}

#     @api.model
#     def release_session_lock(self, session_id, device_id):
#         lock = self.search([('session_id', '=', session_id), ('is_active', '=', True)], limit=1)
#         if not lock:
#             return {'status': 'no_lock'}
#         if lock.device_id != device_id:
#             _logger.warning("[SessionLock] Device %s cannot release lock for session %s", device_id, session_id)
#             return {'status': 'error', 'message': 'Device mismatch'}
#         lock.write({'is_active': False})
#         _logger.info("[SessionLock] Lock released for session %s by device %s", session_id, device_id)
#         return {'status': 'success'}

#     @api.model
#     def cleanup_stale_locks(self):
#         timeout = datetime.now() - timedelta(minutes=5)
#         stale_locks = self.search([('lock_time', '<', timeout), ('is_active', '=', True)])
#         if stale_locks:
#             _logger.info("[SessionLock] Cleaning %d stale locks", len(stale_locks))
#             stale_locks.write({'is_active': False})

#     @api.model
#     def get_active_device_count(self, session_id):
#         """Return the count of active devices for the given session."""
#         return self.search_count([('session_id', '=', session_id), ('is_active', '=', True)])

#     @api.model
#     def long_poll_session_status(self, session_id, device_id):
#         try:
#             self.cleanup_stale_locks()
#             active_count = self.get_active_device_count(session_id)
#             if active_count <= 1 or (active_count > 1 and self.search([('session_id', '=', session_id), ('device_id', '=', device_id), ('is_active', '=', True)], limit=1)):
#                 return {'status': 'free', 'active_devices': active_count}
#             lock = self.search([('session_id', '=', session_id), ('is_active', '=', True), ('device_id', '!=', device_id)], limit=1)
#             if lock:
#                 return {
#                     'status': 'locked',
#                     'device_id': lock.device_id,
#                     'user_name': lock.user_id.name,
#                     'active_devices': active_count
#                 }
#             timeout = datetime.now() + timedelta(seconds=30)
#             while datetime.now() < timeout:
#                 self.env.cr.commit()
#                 active_count = self.get_active_device_count(session_id)
#                 if active_count <= 1 or (active_count > 1 and self.search([('session_id', '=', session_id), ('device_id', '=', device_id), ('is_active', '=', True)], limit=1)):
#                     return {'status': 'free', 'active_devices': active_count}
#                 if active_count > 1:
#                     lock = self.search([('session_id', '=', session_id), ('is_active', '=', True), ('device_id', '!=', device_id)], limit=1)
#                     if lock:
#                         return {
#                             'status': 'locked',
#                             'device_id': lock.device_id,
#                             'user_name': lock.user_id.name,
#                             'active_devices': active_count
#                         }
#                 time.sleep(1)
#             return {'status': 'timeout', 'active_devices': active_count}
#         except Exception as e:
#             _logger.error("[SessionLock] Long poll error for session %s: %s", session_id, str(e))
#             return {'status': 'error', 'message': 'Polling failed, please try again', 'active_devices': 0}


# from odoo import fields, models, api
# import logging
# from datetime import datetime, timedelta
# import time

# _logger = logging.getLogger(__name__)

# class PosSessionLock(models.Model):
#     _name = "pos.session.lock"
#     _description = "POS Session Lock"

#     session_id = fields.Many2one('pos.session', string='Session', required=True, index=True,
#                                domain="[('state', '=', 'opened')]")
#     device_id = fields.Char(string='Device ID', required=True)
#     is_active = fields.Boolean(string='Is Active', default=True)
#     lock_time = fields.Datetime(string='Lock Time', default=fields.Datetime.now)
#     user_id = fields.Many2one('res.users', string='User', required=True)

#     @api.model
#     def create_session_lock(self, session_id, device_id, user_id):
#         _logger.info("[SessionLock] Creating lock for session %s, device %s", session_id, device_id)
#         existing_lock = self.search([('session_id', '=', session_id), ('is_active', '=', True)], limit=1)
#         if existing_lock and existing_lock.device_id != device_id:
#             _logger.warning("[SessionLock] Session %s locked by device %s", session_id, existing_lock.device_id)
#             return {
#                 'status': 'locked',
#                 'device_id': existing_lock.device_id,
#                 'user_name': existing_lock.user_id.name
#             }
#         if existing_lock:
#             existing_lock.write({'lock_time': datetime.now(), 'user_id': user_id})
#         else:
#             self.create({
#                 'session_id': session_id,
#                 'device_id': device_id,
#                 'user_id': user_id,
#                 'lock_time': datetime.now()
#             })
#         return {'status': 'success'}

#     @api.model
#     def check_session_lock(self, session_id, device_id):
#         lock = self.search([('session_id', '=', session_id), ('is_active', '=', True)], limit=1)
#         if lock and lock.device_id != device_id:
#             return {
#                 'status': 'locked',
#                 'device_id': lock.device_id,
#                 'user_name': lock.user_id.name
#             }
#         return {'status': 'free'}

#     @api.model
#     def release_session_lock(self, session_id, device_id):
#         lock = self.search([('session_id', '=', session_id), ('is_active', '=', True)], limit=1)
#         if not lock:
#             return {'status': 'no_lock'}
#         if lock.device_id != device_id:
#             _logger.warning("[SessionLock] Device %s cannot release lock for session %s", device_id, session_id)
#             return {'status': 'error', 'message': 'Device mismatch'}
#         lock.write({'is_active': False})
#         _logger.info("[SessionLock] Lock released for session %s by device %s", session_id, device_id)
#         return {'status': 'success'}

#     @api.model
#     def cleanup_stale_locks(self):
#         timeout = datetime.now() - timedelta(minutes=5)
#         stale_locks = self.search([('lock_time', '<', timeout), ('is_active', '=', True)])
#         if stale_locks:
#             _logger.info("[SessionLock] Cleaning %d stale locks", len(stale_locks))
#             stale_locks.write({'is_active': False})

#     @api.model
#     def get_active_device_count(self, session_id):
#         """Return the count of active devices for the given session."""
#         active_count = self.search_count([('session_id', '=', session_id), ('is_active', '=', True)])
#         _logger.info("[SessionLock] Checked active device count for session %s: %d", session_id, active_count)
#         return active_count

#     @api.model
#     def long_poll_session_status(self, session_id, device_id):
#         try:
#             self.cleanup_stale_locks()
#             active_count = self.get_active_device_count(session_id)
#             _logger.info("[SessionLock] Polling session %s, active devices: %d", session_id, active_count)
#             if active_count <= 1 or (active_count > 1 and self.search([('session_id', '=', session_id), ('device_id', '=', device_id), ('is_active', '=', True)], limit=1)):
#                 return {'status': 'free', 'active_devices': active_count}
#             lock = self.search([('session_id', '=', session_id), ('is_active', '=', True), ('device_id', '!=', device_id)], limit=1)
#             if lock:
#                 return {
#                     'status': 'locked',
#                     'device_id': lock.device_id,
#                     'user_name': lock.user_id.name,
#                     'active_devices': active_count
#                 }
#             timeout = datetime.now() + timedelta(seconds=30)
#             while datetime.now() < timeout:
#                 self.env.cr.commit()
#                 active_count = self.get_active_device_count(session_id)
#                 _logger.info("[SessionLock] Polling loop for session %s, active devices: %d", session_id, active_count)
#                 if active_count <= 1 or (active_count > 1 and self.search([('session_id', '=', session_id), ('device_id', '=', device_id), ('is_active', '=', True)], limit=1)):
#                     return {'status': 'free', 'active_devices': active_count}
#                 if active_count > 1:
#                     lock = self.search([('session_id', '=', session_id), ('is_active', '=', True), ('device_id', '!=', device_id)], limit=1)
#                     if lock:
#                         return {
#                             'status': 'locked',
#                             'device_id': lock.device_id,
#                             'user_name': lock.user_id.name,
#                             'active_devices': active_count
#                         }
#                 time.sleep(1)
#             return {'status': 'timeout', 'active_devices': active_count}
#         except Exception as e:
#             _logger.error("[SessionLock] Long poll error for session %s: %s", session_id, str(e))
#             return {'status': 'error', 'message': 'Polling failed, please try again', 'active_devices': 0}


########################################################################################

# from odoo import fields, models, api
# import logging
# from datetime import datetime, timedelta
# import time

# _logger = logging.getLogger(__name__)

# class PosSessionLock(models.Model):
#     _name = "pos.session.lock"
#     _description = "POS Session Lock"

#     session_id = fields.Many2one('pos.session', string='Session', required=True, index=True,
#                                domain="[('state', '=', 'opened')]")
#     device_id = fields.Char(string='Device ID', required=True)
#     is_active = fields.Boolean(string='Is Active', default=True)
#     lock_time = fields.Datetime(string='Lock Time', default=fields.Datetime.now)
#     user_id = fields.Many2one('res.users', string='User', required=True)

#     @api.model
#     def create_session_lock(self, session_id, device_id, user_id):
#         _logger.info("[SessionLock] Creating lock for session %s, device %s by user %s", session_id, device_id, user_id)
#         existing_lock = self.search([('session_id', '=', session_id), ('is_active', '=', True)], limit=1)
#         if existing_lock and existing_lock.device_id != device_id:
#             _logger.warning("[SessionLock] Session %s locked by device %s", session_id, existing_lock.device_id)
#             return {
#                 'status': 'locked',
#                 'device_id': existing_lock.device_id,
#                 'user_name': existing_lock.user_id.name
#             }
#         if existing_lock:
#             existing_lock.write({'lock_time': datetime.now(), 'user_id': user_id})
#         else:
#             self.create({
#                 'session_id': session_id,
#                 'device_id': device_id,
#                 'user_id': user_id,
#                 'lock_time': datetime.now()
#             })
#         return {'status': 'success'}

#     @api.model
#     def check_session_lock(self, session_id, device_id):
#         lock = self.search([('session_id', '=', session_id), ('is_active', '=', True)], limit=1)
#         if lock and lock.device_id != device_id:
#             return {
#                 'status': 'locked',
#                 'device_id': lock.device_id,
#                 'user_name': lock.user_id.name
#             }
#         return {'status': 'free'}

#     @api.model
#     def release_session_lock(self, session_id, device_id):
#         lock = self.search([('session_id', '=', session_id), ('is_active', '=', True)], limit=1)
#         if not lock:
#             return {'status': 'no_lock'}
#         if lock.device_id != device_id:
#             _logger.warning("[SessionLock] Device %s cannot release lock for session %s", device_id, session_id)
#             return {'status': 'error', 'message': 'Device mismatch'}
#         lock.write({'is_active': False})
#         _logger.info("[SessionLock] Lock released for session %s by device %s", session_id, device_id)
#         return {'status': 'success'}

#     @api.model
#     def cleanup_stale_locks(self):
#         timeout = datetime.now() - timedelta(minutes=5)
#         stale_locks = self.search([('lock_time', '<', timeout), ('is_active', '=', True)])
#         if stale_locks:
#             _logger.info("[SessionLock] Cleaning %d stale locks", len(stale_locks))
#             stale_locks.write({'is_active': False})

#     @api.model
#     def get_active_device_count(self, session_id):
#         """Return the count of active devices for the given session."""
#         active_count = self.search_count([('session_id', '=', session_id), ('is_active', '=', True)])
#         _logger.debug("[SessionLock] Checked active device count for session %s: %d", session_id, active_count)  # Debug level for frequent checks
#         return active_count

#     @api.model
#     def long_poll_session_status(self, session_id, device_id):
#         try:
#             self.cleanup_stale_locks()
#             active_count = self.get_active_device_count(session_id)
#             _logger.info("[SessionLock] Polling session %s, active devices: %d", session_id, active_count)
#             if active_count <= 1 or (active_count > 1 and self.search([('session_id', '=', session_id), ('device_id', '=', device_id), ('is_active', '=', True)], limit=1)):
#                 return {'status': 'free', 'active_devices': active_count}
#             lock = self.search([('session_id', '=', session_id), ('is_active', '=', True), ('device_id', '!=', device_id)], limit=1)
#             if lock:
#                 return {
#                     'status': 'locked',
#                     'device_id': lock.device_id,
#                     'user_name': lock.user_id.name,
#                     'active_devices': active_count
#                 }
#             timeout = datetime.now() + timedelta(seconds=30)
#             while datetime.now() < timeout:
#                 self.env.cr.commit()
#                 active_count = self.get_active_device_count(session_id)
#                 _logger.debug("[SessionLock] Polling loop for session %s, active devices: %d", session_id, active_count)
#                 if active_count <= 1 or (active_count > 1 and self.search([('session_id', '=', session_id), ('device_id', '=', device_id), ('is_active', '=', True)], limit=1)):
#                     return {'status': 'free', 'active_devices': active_count}
#                 if active_count > 1:
#                     lock = self.search([('session_id', '=', session_id), ('is_active', '=', True), ('device_id', '!=', device_id)], limit=1)
#                     if lock:
#                         return {
#                             'status': 'locked',
#                             'device_id': lock.device_id,
#                             'user_name': lock.user_id.name,
#                             'active_devices': active_count
#                         }
#                 time.sleep(1)
#             return {'status': 'timeout', 'active_devices': active_count}
#         except Exception as e:
#             _logger.error("[SessionLock] Long poll error for session %s: %s", session_id, str(e))
#             return {'status': 'error', 'message': 'Polling failed, please try again', 'active_devices': 0}



# from odoo import models, fields, api
# import logging
# from datetime import datetime, timedelta
# import time

# _logger = logging.getLogger(__name__)

# class PosSessionLock(models.Model):
#     _name = "pos.session.lock"
#     _description = "POS Session Lock"

#     session_id = fields.Many2one('pos.session', string='Session', required=True, index=True,
#                                  domain="[('state', '=', 'opened')]")
#     device_id = fields.Char(string='Device ID', required=True, index=True)
#     is_active = fields.Boolean(string='Is Active', default=True)
#     lock_time = fields.Datetime(string='Lock Time', default=fields.Datetime.now)
#     user_id = fields.Many2one('res.users', string='User', required=True)

#     @api.model
#     def create_session_lock(self, session_id, device_id, user_id):
#         _logger.info("[SessionLock] Attempting to create lock for session %s, device %s, user %s", session_id, device_id, user_id)
#         if not all([session_id, device_id, user_id]):
#             _logger.error("[SessionLock] Invalid parameters: session_id=%s, device_id=%s, user_id=%s", session_id, device_id, user_id)
#             return {'status': 'error', 'message': 'Missing session_id, device_id, or user_id'}

#         # Check if session exists and is open
#         session = self.env['pos.session'].search([('id', '=', session_id), ('state', '=', 'opened')], limit=1)
#         if not session:
#             _logger.error("[SessionLock] Session %s does not exist or is not open", session_id)
#             return {'status': 'error', 'message': 'Session does not exist or is not open'}

#         # Check for existing active lock with different device_id
#         existing_lock = self.search([('session_id', '=', session_id), ('device_id', '!=', device_id), ('is_active', '=', True)], limit=1)
#         if existing_lock:
#             _logger.warning("[SessionLock] Session %s is locked by device %s (user: %s)", session_id, existing_lock.device_id, existing_lock.user_id.name)
#             return {
#                 'status': 'locked',
#                 'device_id': existing_lock.device_id,
#                 'user_name': existing_lock.user_id.name
#             }

#         # Check for existing lock with same device_id
#         current_lock = self.search([('session_id', '=', session_id), ('device_id', '=', device_id), ('is_active', '=', True)], limit=1)
#         if current_lock:
#             _logger.info("[SessionLock] Updating existing lock for session %s, device %s", session_id, device_id)
#             current_lock.write({'lock_time': datetime.now(), 'user_id': user_id})
#         else:
#             _logger.info("[SessionLock] Creating new lock for session %s, device %s", session_id, device_id)
#             self.create({
#                 'session_id': session_id,
#                 'device_id': device_id,
#                 'user_id': user_id,
#                 'lock_time': datetime.now()
#             })
#         return {'status': 'success'}

#     @api.model
#     def check_device_in_session(self, session_id, device_id):
#         """Check if the device_id is associated with an active session."""
#         _logger.info("[SessionLock] Checking if device %s is in session %s", device_id, session_id)
#         lock = self.search([('session_id', '=', session_id), ('device_id', '=', device_id), ('is_active', '=', True)], limit=1)
#         if lock:
#             _logger.info("[SessionLock] Device %s is active in session %s", device_id, session_id)
#             return {'status': 'active', 'user_name': lock.user_id.name}
#         _logger.info("[SessionLock] Device %s is not active in session %s", device_id, session_id)
#         return {'status': 'inactive'}

#     @api.model
#     def check_session_lock(self, session_id, device_id):
#         lock = self.search([('session_id', '=', session_id), ('is_active', '=', True)], limit=1)
#         if lock and lock.device_id != device_id:
#             return {
#                 'status': 'locked',
#                 'device_id': lock.device_id,
#                 'user_name': lock.user_id.name
#             }
#         return {'status': 'free'}

#     @api.model
#     def release_session_lock(self, session_id, device_id):
#         lock = self.search([('session_id', '=', session_id), ('device_id', '=', device_id), ('is_active', '=', True)], limit=1)
#         if not lock:
#             _logger.info("[SessionLock] No active lock found for session %s, device %s", session_id, device_id)
#             return {'status': 'no_lock'}
#         lock.write({'is_active': False})
#         _logger.info("[SessionLock] Lock released for session %s by device %s", session_id, device_id)
#         return {'status': 'success'}

#     @api.model
#     def cleanup_stale_locks(self):
#         timeout = datetime.now() - timedelta(minutes=5)
#         stale_locks = self.search([('lock_time', '<', timeout), ('is_active', '=', True)])
#         if stale_locks:
#             _logger.info("[SessionLock] Cleaning %d stale locks", len(stale_locks))
#             stale_locks.write({'is_active': False})

#     @api.model
#     def get_active_device_count(self, session_id):
#         """Return the count of active devices for the given session."""
#         active_count = self.search_count([('session_id', '=', session_id), ('is_active', '=', True)])
#         _logger.debug("[SessionLock] Checked active device count for session %s: %d", session_id, active_count)
#         return active_count

#     @api.model
#     def long_poll_session_status(self, session_id, device_id):
#         try:
#             _logger.info("[SessionLock] Polling session %s for device %s", session_id, device_id)
#             self.cleanup_stale_locks()
#             # Update lock_time for the current device to prevent stale cleanup
#             lock = self.search([('session_id', '=', session_id), ('device_id', '=', device_id), ('is_active', '=', True)], limit=1)
#             if lock:
#                 lock.write({'lock_time': datetime.now()})
#                 _logger.debug("[SessionLock] Updated lock_time for session %s, device %s", session_id, device_id)
#             active_count = self.get_active_device_count(session_id)
#             if active_count <= 1 or lock:
#                 return {'status': 'free', 'active_devices': active_count}
#             other_lock = self.search([('session_id', '=', session_id), ('is_active', '=', True), ('device_id', '!=', device_id)], limit=1)
#             if other_lock:
#                 return {
#                     'status': 'locked',
#                     'device_id': other_lock.device_id,
#                     'user_name': other_lock.user_id.name,
#                     'active_devices': active_count
#                 }
#             timeout = datetime.now() + timedelta(seconds=30)
#             while datetime.now() < timeout:
#                 self.env.cr.commit()
#                 active_count = self.get_active_device_count(session_id)
#                 lock = self.search([('session_id', '=', session_id), ('device_id', '=', device_id), ('is_active', '=', True)], limit=1)
#                 if active_count <= 1 or lock:
#                     return {'status': 'free', 'active_devices': active_count}
#                 other_lock = self.search([('session_id', '=', session_id), ('is_active', '=', True), ('device_id', '!=', device_id)], limit=1)
#                 if other_lock:
#                     return {
#                         'status': 'locked',
#                         'device_id': other_lock.device_id,
#                         'user_name': other_lock.user_id.name,
#                         'active_devices': active_count
#                     }
#                 time.sleep(1)
#             return {'status': 'timeout', 'active_devices': active_count}
#         except Exception as e:
#             _logger.error("[SessionLock] Long poll error for session %s: %s", session_id, str(e))
#             return {'status': 'error', 'message': 'Polling failed, please try again', 'active_devices': 0}





# from odoo import models, fields, api
# import logging
# from datetime import datetime, timedelta
# import time

# _logger = logging.getLogger(__name__)

# class PosSessionLock(models.Model):
#     _name = "pos.session.lock"
#     _description = "POS Session Lock"

#     session_id = fields.Many2one('pos.session', string='Session', required=True, index=True,
#                                  domain="[('state', '=', 'opened')]")
#     device_id = fields.Char(string='Device ID', required=True, index=True)
#     is_active = fields.Boolean(string='Is Active', default=True)
#     lock_time = fields.Datetime(string='Lock Time', default=fields.DateTime.now)
#     user_id = fields.Many2one('res.users', string='User', required=True)

#     @api.model
#     def create_session_lock(self, session_id, device_id, user_id):
#         _logger.info("[SessionLock] Attempting to create lock for session %s, device %s, user %s", session_id, device_id, user_id)
#         if not all([session_id, device_id, user_id]):
#             _logger.error("[SessionLock] Invalid parameters: session_id=%s, device_id=%s, user_id=%s", session_id, device_id, user_id)
#             return {'status': 'error', 'message': 'Missing session_id, device_id, or user_id'}

#         # Check if session exists and is open
#         session = self.env['pos.session'].search([('id', '=', session_id), ('state', '=', 'opened')], limit=1)
#         if not session:
#             _logger.error("[SessionLock] Session %s does not exist or is not open", session_id)
#             return {'status': 'error', 'message': 'Session does not exist or is not open'}

#         # Check for existing lock with same device_id
#         current_lock = self.search([('session_id', '=', session_id), ('device_id', '=', device_id), ('is_active', '=', True)], limit=1)
#         if current_lock:
#             _logger.info("[SessionLock] Updating existing lock for session %s, device %s", session_id, device_id)
#             current_lock.write({'lock_time': datetime.now(), 'user_id': user_id})
#         else:
#             _logger.info("[SessionLock] Creating new lock for session %s, device %s", session_id, device_id)
#             self.create({
#                 'session_id': session_id,
#                 'device_id': device_id,
#                 'user_id': user_id

#             })
#         _logger.info("[SessionLock] Successfully created/updated lock for session %s, device %s", session_id, device_id)
#         return {'status': 'success'}

#     @api.model
#     def check_device_in_session(self, session_id, device_id):
#         """Check if the device_id is associated with an active session."""
#         _logger.info("[SessionLock] Checking if device %s is in session %s", device_id, session_id)
#         lock = self.search([('session_id', '=', session_id), ('device_id', '=', device_id), ('is_active', '=', True)], limit=1)
#         if lock:
#             _logger.info("[SessionLock] Device %s is active in session %s", device_id, session_id)
#             return {'status': 'active', 'user_name': lock.user_id.name}
#         _logger.info("[SessionLock] Device %s is not active in session %s", device_id, session_id)
#         return {'status': 'inactive'}

#     @api.model
#     def check_session_lock(self, session_id, device_id):
#         _logger.info("[SessionLock] Checking lock status for session %s, device %s", session_id, device_id)
#         lock = self.search([('session_id', '=', session_id), ('is_active', '=', True)], limit=1)
#         if lock and lock.device_id != device_id:
#             _logger.warning("[SessionLock] Session %s locked by device %s", session_id, lock.device_id)
#             return {
#                 'status': 'locked',
#                 'device_id': lock.device_id,
#                 'user_name': lock.user_id.name
#             }
#         _logger.info("[SessionLock] Session %s is free for device %s", session_id, device_id)
#         return {'status': 'free'}

#     @api.model
#     def release_session_lock(self, session_id, device_id):
#         _logger.info("[SessionLock] Attempting to release lock for session %s, device %s", session_id, device_id)
#         lock = self.search([('session_id', '=', session_id), ('device_id', '=', device_id), ('is_active', '=', True)], limit=1)
#         if not lock:
#             _logger.info("[SessionLock] No active lock found for session %s, device %s", session_id, device_id)
#             return {'status': 'no_lock'}
#         lock.write({'is_active': False})
#         _logger.info("[SessionLock] Lock released for session %s by device %s", session_id, device_id)
#         return {'status': 'success'}

#     @api.model
#     def cleanup_stale_locks(self):
#         timeout = datetime.now() - timedelta(minutes=5)
#         stale_locks = self.search([('lock_time', '<', timeout), ('is_active', '=', True)])
#         if stale_locks:
#             _logger.info("[SessionLock] Cleaning %d stale locks", len(stale_locks))
#             stale_locks.write({'is_active': False})

#     @api.model
#     def get_active_device_count(self, session_id):
#         """Return the count of active devices for the given session."""
#         active_count = self.search_count([('session_id', '=', session_id), ('is_active', '=', True)])
#         _logger.debug("[SessionLock] Checked active device count for session %s: %d", session_id, active_count)
#         return active_count

#     @api.model
#     def long_poll_session_status(self, session_id, device_id):
#         try:
#             _logger.info("[SessionLock] Polling session %s for device %s", session_id, device_id)
#             self.cleanup_stale_locks()
#             lock = self.search([('session_id', '=', session_id), ('device_id', '=', device_id), ('is_active', '=', True)], limit=1)
#             if lock:
#                 lock.write({'lock_time': datetime.now()})
#                 _logger.debug("[SessionLock] Updated lock_time for session %s, device %s", session_id, device_id)
#             active_count = self.get_active_device_count(session_id)
#             _logger.info("[SessionLock] Active devices for session %s: %d", session_id, active_count)
#             if active_count <= 1 or lock:
#                 return {'status': 'free', 'active_devices': active_count}
#             other_lock = self.search([('session_id', '=', session_id), ('is_active', '=', True), ('device_id', '!=', device_id)], limit=1)
#             if other_lock:
#                 return {
#                     'status': 'locked',
#                     'device_id': other_lock.device_id,
#                     'user_name': other_lock.user_id.name,
#                     'active_devices': active_count
#                 }
#             timeout = datetime.now() + timedelta(seconds=30)
#             while datetime.now() < timeout:
#                 self.env.cr.commit()
#                 active_count = self.get_active_device_count(session_id)
#                 lock = self.search([('session_id', '=', session_id), ('device_id', '=', device_id), ('is_active', '=', True)], limit=1)
#                 if active_count <= 1 or lock:
#                     return {'status': 'free', 'active_devices': active_count}
#                 other_lock = self.search([('session_id', '=', session_id), ('is_active', '=', True), ('device_id', '!=', device_id)], limit=1)
#                 if other_lock:
#                     return {
#                         'status': 'locked',
#                         'device_id': other_lock.device_id,
#                         'user_name': other_lock.user_id.name,
#                         'active_devices': active_count
#                     }
#                 time.sleep(1)
#             _logger.warning("[SessionLock] Polling timeout for session %s, device %s", session_id, device_id)
#             return {'status': 'timeout', 'active_devices': active_count}
#         except Exception as e:
#             _logger.error("[SessionLock] Long poll error for session %s: %s", session_id, str(e))
#             return {'status': 'error', 'message': str(e), 'active_devices': 0}




# from odoo import models, fields, api
# import logging
# from datetime import datetime, timedelta
# import time

# _logger = logging.getLogger(__name__)

# # Log module initialization
# _logger.info("[SessionLock] Initializing pos_session_lock module")

# class PosSessionLock(models.Model):
#     _name = "pos.session.lock"
#     _description = "POS Session Lock"

#     session_id = fields.Many2one('pos.session', string='Session', required=True, index=True,
#                                  domain="[('state', '=', 'opened')]")
#     device_id = fields.Char(string='Device ID', required=True, index=True)
#     is_active = fields.Boolean(string='Is Active', default=True)
#     lock_time = fields.Datetime(string='Lock Time', default=fields.Datetime.now)
#     user_id = fields.Many2one('res.users', string='User', required=True)

#     @api.model
#     def create_session_lock(self, session_id, device_id, user_id):
#         _logger.info("[SessionLock] Attempting to create lock for session %s, device %s, user %s", session_id, device_id, user_id)
#         if not all([session_id, device_id, user_id]):
#             _logger.error("[SessionLock] Invalid parameters: session_id=%s, device_id=%s, user_id=%s", session_id, device_id, user_id)
#             return {'status': 'error', 'message': 'Missing session_id, device_id, or user_id'}

#         # Check if session exists and is open
#         session = self.env['pos.session'].search([('id', '=', session_id), ('state', '=', 'opened')], limit=1)
#         if not session:
#             _logger.error("[SessionLock] Session %s does not exist or is not open", session_id)
#             return {'status': 'error', 'message': 'Session does not exist or is not open'}

#         # Check for existing lock with same device_id
#         current_lock = self.search([('session_id', '=', session_id), ('device_id', '=', device_id), ('is_active', '=', True)], limit=1)
#         if current_lock:
#             _logger.info("[SessionLock] Updating existing lock for session %s, device %s", session_id, device_id)
#             current_lock.write({'lock_time': datetime.now(), 'user_id': user_id})
#         else:
#             _logger.info("[SessionLock] Creating new lock for session %s, device %s", session_id, device_id)
#             self.create({
#                 'session_id': session_id,
#                 'device_id': device_id,
#                 'user_id': user_id,
#                 'lock_time': datetime.now()
#             })
#         _logger.info("[SessionLock] Successfully created/updated lock for session %s, device %s", session_id, device_id)
#         return {'status': 'success'}

#     @api.model
#     def check_device_in_session(self, session_id, device_id):
#         """Check if the device_id is associated with an active session."""
#         _logger.info("[SessionLock] Checking if device %s is in session %s", device_id, session_id)
#         lock = self.search([('session_id', '=', session_id), ('device_id', '=', device_id), ('is_active', '=', True)], limit=1)
#         if lock:
#             _logger.info("[SessionLock] Device %s is active in session %s", device_id, session_id)
#             return {'status': 'active', 'user_name': lock.user_id.name}
#         _logger.info("[SessionLock] Device %s is not active in session %s", device_id, session_id)
#         return {'status': 'inactive'}

#     @api.model
#     def check_session_lock(self, session_id, device_id):
#         _logger.info("[SessionLock] Checking lock status for session %s, device %s", session_id, device_id)
#         lock = self.search([('session_id', '=', session_id), ('is_active', '=', True)], limit=1)
#         if lock and lock.device_id != device_id:
#             _logger.warning("[SessionLock] Session %s locked by device %s", session_id, lock.device_id)
#             return {
#                 'status': 'locked',
#                 'device_id': lock.device_id,
#                 'user_name': lock.user_id.name
#             }
#         _logger.info("[SessionLock] Session %s is free for device %s", session_id, device_id)
#         return {'status': 'free'}

#     @api.model
#     def release_session_lock(self, session_id, device_id):
#         _logger.info("[SessionLock] Attempting to release lock for session %s, device %s", session_id, device_id)
#         lock = self.search([('session_id', '=', session_id), ('device_id', '=', device_id), ('is_active', '=', True)], limit=1)
#         if not lock:
#             _logger.info("[SessionLock] No active lock found for session %s, device %s", session_id, device_id)
#             return {'status': 'no_lock'}
#         lock.write({'is_active': False})
#         _logger.info("[SessionLock] Lock released for session %s by device %s", session_id, device_id)
#         return {'status': 'success'}

#     @api.model
#     def cleanup_stale_locks(self):
#         timeout = datetime.now() - timedelta(minutes=5)
#         stale_locks = self.search([('lock_time', '<', timeout), ('is_active', '=', True)])
#         if stale_locks:
#             _logger.info("[SessionLock] Cleaning %d stale locks", len(stale_locks))
#             stale_locks.write({'is_active': False})

#     @api.model
#     def get_active_device_count(self, session_id):
#         """Return the count of active devices for the given session."""
#         active_count = self.search_count([('session_id', '=', session_id), ('is_active', '=', True)])
#         _logger.debug("[SessionLock] Checked active device count for session %s: %d", session_id, active_count)
#         return active_count

#     @api.model
#     def long_poll_session_status(self, session_id, device_id):
#         try:
#             _logger.info("[SessionLock] Polling session %s for device %s", session_id, device_id)
#             self.cleanup_stale_locks()
#             lock = self.search([('session_id', '=', session_id), ('device_id', '=', device_id), ('is_active', '=', True)], limit=1)
#             if lock:
#                 lock.write({'lock_time': datetime.now()})
#                 _logger.debug("[SessionLock] Updated lock_time for session %s, device %s", session_id, device_id)
#             active_count = self.get_active_device_count(session_id)
#             _logger.info("[SessionLock] Active devices for session %s: %d", session_id, active_count)
#             if active_count <= 1 or lock:
#                 return {'status': 'free', 'active_devices': active_count}
#             other_lock = self.search([('session_id', '=', session_id), ('is_active', '=', True), ('device_id', '!=', device_id)], limit=1)
#             if other_lock:
#                 return {
#                     'status': 'locked',
#                     'device_id': other_lock.device_id,
#                     'user_name': other_lock.user_id.name,
#                     'active_devices': active_count
#                 }
#             timeout = datetime.now() + timedelta(seconds=30)
#             while datetime.now() < timeout:
#                 self.env.cr.commit()
#                 active_count = self.get_active_device_count(session_id)
#                 lock = self.search([('session_id', '=', session_id), ('device_id', '=', device_id), ('is_active', '=', True)], limit=1)
#                 if active_count <= 1 or lock:
#                     return {'status': 'free', 'active_devices': active_count}
#                 other_lock = self.search([('session_id', '=', session_id), ('is_active', '=', True), ('device_id', '!=', device_id)], limit=1)
#                 if other_lock:
#                     return {
#                         'status': 'locked',
#                         'device_id': other_lock.device_id,
#                         'user_name': other_lock.user_id.name,
#                         'active_devices': active_count
#                     }
#                 time.sleep(1)
#             _logger.warning("[SessionLock] Polling timeout for session %s, device %s", session_id, device_id)
#             return {'status': 'timeout', 'active_devices': active_count}
#         except Exception as e:
#             _logger.error("[SessionLock] Long poll error for session %s: %s", session_id, str(e))
#             return {'status': 'error', 'message': str(e), 'active_devices': 0}




# from odoo import models, fields, api
# import logging
# from datetime import datetime, timedelta
# import time

# _logger = logging.getLogger(__name__)

# # Log module initialization
# _logger.info("[SessionLock] Initializing pos_session_lock module")

# class PosSessionLock(models.Model):
#     _name = "pos.session.lock"
#     _description = "POS Session Lock"

#     session_id = fields.Many2one('pos.session', string='Session', required=True, index=True,
#                                  domain="[('state', '=', 'opened')]")
#     device_id = fields.Char(string='Device ID', required=True, index=True)
#     is_active = fields.Boolean(string='Is Active', default=True)
#     lock_time = fields.Datetime(string='Lock Time', default=fields.Datetime.now)  #i think i dont need this
#     user_id = fields.Many2one('res.users', string='User', required=True)

#     @api.model
#     def create_session_lock(self, session_id, device_id, user_id):
#         _logger.info("[SessionLock] RPC call to create lock for session %s, device %s, user %s", session_id, device_id, user_id)
#         if not all([session_id, device_id, user_id]):
#             _logger.error("[SessionLock] Invalid parameters: session_id=%s, device_id=%s, user_id=%s", session_id, device_id, user_id)
#             return {'status': 'error', 'message': 'Missing session_id, device_id, or user_id'}

#         # Check if session exists and is open
#         session = self.env['pos.session'].search([('id', '=', session_id), ('state', '=', 'opened')], limit=1)
#         if not session:
#             _logger.error("[SessionLock] Session %s does not exist or is not open", session_id)
#             return {'status': 'error', 'message': 'Session does not exist or is not open'}

#         # Check for existing lock with same device_id
#         current_lock = self.search([('session_id', '=', session_id), ('device_id', '=', device_id), ('is_active', '=', True)], limit=1)
#         if current_lock:
#             _logger.info("[SessionLock] Updating existing lock for session %s, device %s", session_id, device_id)
#             current_lock.write({'lock_time': datetime.now(), 'user_id': user_id})
#         else:
#             _logger.info("[SessionLock] Creating new lock for session %s, device %s", session_id, device_id)
#             self.create({
#                 'session_id': session_id,
#                 'device_id': device_id,
#                 'user_id': user_id,
#                 'lock_time': datetime.now()
#             })
#         _logger.info("[SessionLock] Successfully created/updated lock for session %s, device %s", session_id, device_id)
#         return {'status': 'success'}

#     @api.model
#     def check_device_in_session(self, session_id, device_id):
#         """Check if the device_id is associated with an active session."""
#         _logger.info("[SessionLock] RPC call to check device %s in session %s", device_id, session_id)
#         lock = self.search([('session_id', '=', session_id), ('device_id', '=', device_id), ('is_active', '=', True)], limit=1)
#         if lock:
#             _logger.info("[SessionLock] Device %s is active in session %s", device_id, session_id)
#             return {'status': 'active', 'user_name': lock.user_id.name}
#         _logger.info("[SessionLock] Device %s is not active in session %s", device_id, session_id)
#         return {'status': 'inactive'}

#     @api.model
#     def check_session_lock(self, session_id, device_id):
#         _logger.info("[SessionLock] RPC call to check lock status for session %s, device %s", session_id, device_id)
#         lock = self.search([('session_id', '=', session_id), ('is_active', '=', True)], limit=1)
#         if lock and lock.device_id != device_id:
#             _logger.warning("[SessionLock] Session %s locked by device %s", session_id, lock.device_id)
#             return {
#                 'status': 'locked',
#                 'device_id': lock.device_id,
#                 'user_name': lock.user_id.name
#             }
#         _logger.info("[SessionLock] Session %s is free for device %s", session_id, device_id)
#         return {'status': 'free'}

#     @api.model
#     def release_session_lock(self, session_id, device_id):
#         _logger.info("[SessionLock] RPC call to release lock for session %s, device %s", session_id, device_id)
#         lock = self.search([('session_id', '=', session_id), ('device_id', '=', device_id), ('is_active', '=', True)], limit=1)
#         if not lock:
#             _logger.info("[SessionLock] No active lock found for session %s, device %s", session_id, device_id)
#             return {'status': 'no_lock'}
#         lock.write({'is_active': False})
#         _logger.info("[SessionLock] Lock released for session %s by device %s", session_id, device_id)
#         return {'status': 'success'}

#     @api.model
#     def cleanup_stale_locks(self):
#         timeout = datetime.now() - timedelta(minutes=5)
#         stale_locks = self.search([('lock_time', '<', timeout), ('is_active', '=', True)])
#         if stale_locks:
#             _logger.info("[SessionLock] Cleaning %d stale locks", len(stale_locks))
#             stale_locks.write({'is_active': False})

#     @api.model
#     def get_active_device_count(self, session_id):
#         """Return the count of active devices for the given session."""
#         active_count = self.search_count([('session_id', '=', session_id), ('is_active', '=', True)])
#         _logger.debug("[SessionLock] Checked active device count for session %s: %d", session_id, active_count)
#         return active_count

#     @api.model
#     def long_poll_session_status(self, session_id, device_id):
#         try:
#             _logger.info("[SessionLock] RPC call to poll session %s for device %s", session_id, device_id)
#             self.cleanup_stale_locks()
#             lock = self.search([('session_id', '=', session_id), ('device_id', '=', device_id), ('is_active', '=', True)], limit=1)
#             if lock:
#                 lock.write({'lock_time': datetime.now()})
#                 _logger.debug("[SessionLock] Updated lock_time for session %s, device %s", session_id, device_id)
#             active_count = self.get_active_device_count(session_id)
#             _logger.info("[SessionLock] Active devices for session %s: %d", session_id, active_count)
#             if active_count <= 1 or lock:
#                 return {'status': 'free', 'active_devices': active_count}
#             other_lock = self.search([('session_id', '=', session_id), ('is_active', '=', True), ('device_id', '!=', device_id)], limit=1)
#             if other_lock:
#                 return {
#                     'status': 'locked',
#                     'device_id': other_lock.device_id,
#                     'user_name': other_lock.user_id.name,
#                     'active_devices': active_count
#                 }
#             timeout = datetime.now() + timedelta(seconds=30)
#             while datetime.now() < timeout:
#                 self.env.cr.commit()
#                 active_count = self.get_active_device_count(session_id)
#                 lock = self.search([('session_id', '=', session_id), ('device_id', '=', device_id), ('is_active', '=', True)], limit=1)
#                 if active_count <= 1 or lock:
#                     return {'status': 'free', 'active_devices': active_count}
#                 other_lock = self.search([('session_id', '=', session_id), ('is_active', '=', True), ('device_id', '!=', device_id)], limit=1)
#                 if other_lock:
#                     return {
#                         'status': 'locked',
#                         'device_id': other_lock.device_id,
#                         'user_name': other_lock.user_id.name,
#                         'active_devices': active_count
#                     }
#                 time.sleep(1)
#             _logger.warning("[SessionLock] Polling timeout for session %s, device %s", session_id, device_id)
#             return {'status': 'timeout', 'active_devices': active_count}
#         except Exception as e:
#             _logger.error("[SessionLock] Long poll error for session %s: %s", session_id, str(e))
#             return {'status': 'error', 'message': str(e), 'active_devices': 0}





# from odoo import models, fields, api
# import logging
# from datetime import datetime, timedelta
# import time

# _logger = logging.getLogger(__name__)

# _logger.info("[SessionLock] Initializing pos_session_lock module")

# class PosSessionLock(models.Model):
#     _name = "pos.session.lock"
#     _description = "POS Session Lock"

#     session_id = fields.Many2one('pos.session', string='Session', required=True, index=True,
#                                  domain="[('state', '=', 'opened')]")
#     device_id = fields.Char(string='Device ID', required=True, index=True)
#     is_active = fields.Boolean(string='Is Active', default=True)
#     lock_time = fields.Datetime(string='Lock Time', default=fields.Datetime.now)
#     user_id = fields.Many2one('res.users', string='User', required=True)

#     @api.model
#     def create_session_lock(self, session_id, device_id, user_id):
#         _logger.info("[SessionLock] Creating lock for session %s, device %s, user %s", session_id, device_id, user_id)
#         if not all([session_id, device_id, user_id]):
#             _logger.error("[SessionLock] Invalid parameters: session_id=%s, device_id=%s, user_id=%s", session_id, device_id, user_id)
#             return {'status': 'error', 'message': 'Missing session_id, device_id, or user_id'}

#         session = self.env['pos.session'].search([('id', '=', session_id), ('state', '=', 'opened')], limit=1)
#         if not session:
#             _logger.error("[SessionLock] Session %s does not exist or is not open", session_id)
#             return {'status': 'error', 'message': 'Session does not exist or is not open'}

#         current_lock = self.search([('session_id', '=', session_id), ('device_id', '=', device_id)], limit=1)
#         if current_lock:
#             _logger.info("[SessionLock] Updating existing lock for session %s, device %s", session_id, device_id)
#             current_lock.write({'is_active': True, 'lock_time': datetime.now(), 'user_id': user_id})
#         else:
#             _logger.info("[SessionLock] Creating new lock for session %s, device %s", session_id, device_id)
#             self.create({
#                 'session_id': session_id,
#                 'device_id': device_id,
#                 'user_id': user_id,
#                 'lock_time': datetime.now(),
#                 'is_active': True
#             })
#         active_count = self.get_active_device_count(session_id)
#         _logger.info("[SessionLock] Lock created/updated for session %s, device %s, active devices: %d", session_id, device_id, active_count)
#         return {'status': 'success', 'active_devices': active_count}

#     @api.model
#     def check_device_in_session(self, session_id, device_id):
#         _logger.info("[SessionLock] Checking device %s in session %s", device_id, session_id)
#         lock = self.search([('session_id', '=', session_id), ('device_id', '=', device_id), ('is_active', '=', True)], limit=1)
#         if lock:
#             _logger.info("[SessionLock] Device %s is active in session %s", device_id, session_id)
#             return {'status': 'active', 'user_name': lock.user_id.name}
#         _logger.info("[SessionLock] Device %s is not active in session %s", device_id, session_id)
#         return {'status': 'inactive'}

#     @api.model
#     def check_session_lock(self, session_id, device_id):
#         _logger.info("[SessionLock] Checking lock status for session %s, device %s", session_id, device_id)
#         lock = self.search([('session_id', '=', session_id), ('is_active', '=', True), ('device_id', '!=', device_id)], limit=1)
#         if lock:
#             _logger.warning("[SessionLock] Session %s locked by device %s", session_id, lock.device_id)
#             return {
#                 'status': 'locked',
#                 'device_id': lock.device_id,
#                 'user_name': lock.user_id.name
#             }
#         _logger.info("[SessionLock] Session %s is free for device %s", session_id, device_id)
#         return {'status': 'free'}

#     @api.model
#     def release_session_lock(self, session_id, device_id):
#         _logger.info("[SessionLock] Releasing lock for session %s, device %s", session_id, device_id)
#         lock = self.search([('session_id', '=', session_id), ('device_id', '=', device_id), ('is_active', '=', True)], limit=1)
#         if not lock:
#             _logger.info("[SessionLock] No active lock found for session %s, device %s", session_id, device_id)
#             return {'status': 'no_lock'}
#         lock.write({'is_active': False, 'lock_time': datetime.now()})
#         active_count = self.get_active_device_count(session_id)
#         _logger.info("[SessionLock] Lock released for session %s by device %s, active devices: %d", session_id, device_id, active_count)
#         return {'status': 'success', 'active_devices': active_count}

#     @api.model
#     def cleanup_stale_locks(self):
#         timeout = datetime.now() - timedelta(minutes=5)
#         stale_locks = self.search([('lock_time', '<', timeout), ('is_active', '=', True)])
#         if stale_locks:
#             _logger.info("[SessionLock] Cleaning %d stale locks", len(stale_locks))
#             stale_locks.write({'is_active': False})
#         return len(stale_locks)

#     @api.model
#     def get_active_device_count(self, session_id):
#         active_count = self.search_count([('session_id', '=', session_id), ('is_active', '=', True)])
#         _logger.debug("[SessionLock] Active device count for session %s: %d", session_id, active_count)
#         return active_count

#     @api.model
#     def get_active_devices(self, session_id):
#         locks = self.search([('session_id', '=', session_id), ('is_active', '=', True)])
#         devices = [{'device_id': lock.device_id, 'user_name': lock.user_id.name} for lock in locks]
#         _logger.debug("[SessionLock] Active devices for session %s: %s", session_id, devices)
#         return devices

#     @api.model
#     def long_poll_session_status(self, session_id, device_id):
#         try:
#             _logger.info("[SessionLock] Polling session %s for device %s", session_id, device_id)
#             self.cleanup_stale_locks()
#             lock = self.search([('session_id', '=', session_id), ('device_id', '=', device_id), ('is_active', '=', True)], limit=1)
#             if lock:
#                 lock.write({'lock_time': datetime.now()})
#                 _logger.debug("[SessionLock] Updated lock_time for session %s, device %s", session_id, device_id)
            
#             active_count = self.get_active_device_count(session_id)
#             active_devices = self.get_active_devices(session_id)
#             _logger.info("[SessionLock] Active devices for session %s: %d", session_id, active_count)
            
#             if active_count <= 1:
#                 return {'status': 'free', 'active_devices': active_devices, 'device_count': active_count}
            
#             other_lock = self.search([('session_id', '=', session_id), ('is_active', '=', True), ('device_id', '!=', device_id)], limit=1)
#             if other_lock:
#                 return {
#                     'status': 'locked',
#                     'device_id': other_lock.device_id,
#                     'user_name': other_lock.user_id.name,
#                     'active_devices': active_devices,
#                     'device_count': active_count
#                 }
            
#             timeout = datetime.now() + timedelta(seconds=30)
#             while datetime.now() < timeout:
#                 self.env.cr.commit()
#                 active_count = self.get_active_device_count(session_id)
#                 active_devices = self.get_active_devices(session_id)
#                 lock = self.search([('session_id', '=', session_id), ('device_id', '=', device_id), ('is_active', '=', True)], limit=1)
#                 if active_count <= 1:
#                     return {'status': 'free', 'active_devices': active_devices, 'device_count': active_count}
#                 other_lock = self.search([('session_id', '=', session_id), ('is_active', '=', True), ('device_id', '!=', device_id)], limit=1)
#                 if other_lock:
#                     return {
#                         'status': 'locked',
#                         'device_id': other_lock.device_id,
#                         'user_name': other_lock.user_id.name,
#                         'active_devices': active_devices,
#                         'device_count': active_count
#                     }
#                 time.sleep(1)
#             _logger.warning("[SessionLock] Polling timeout for session %s, device %s", session_id, device_id)
#             return {'status': 'timeout', 'active_devices': active_devices, 'device_count': active_count}
#         except Exception as e:
#             _logger.error("[SessionLock] Long poll error for session %s: %s", session_id, str(e))
#             return {'status': 'error', 'message': str(e), 'active_devices': [], 'device_count': 0}




# from odoo import models, fields, api
# import logging
# from datetime import datetime, timedelta
# import time

# _logger = logging.getLogger(__name__)

# _logger.info("[SessionLock] Initializing pos_session_lock module")

# class PosSessionLock(models.Model):
#     _name = "pos.session.lock"
#     _description = "POS Session Lock"

#     session_id = fields.Many2one('pos.session', string='Session', required=True, index=True,
#                                  domain="[('state', '=', 'opened')]")
#     device_id = fields.Char(string='Device ID', required=True, index=True)
#     is_active = fields.Boolean(string='Is Active', default=True)
#     lock_time = fields.Datetime(string='Lock Time', default=fields.Datetime.now)
#     user_id = fields.Many2one('res.users', string='User', required=True)

#     @api.model
#     def create_session_lock(self, session_id, device_id, user_id):
#         _logger.info("[SessionLock] Creating lock for session %s, device %s, user %s", session_id, device_id, user_id)
#         if not all([session_id, device_id, user_id]):
#             _logger.error("[SessionLock] Invalid parameters: session_id=%s, device_id=%s, user_id=%s", session_id, device_id, user_id)
#             return {'status': 'error', 'message': 'Missing session_id, device_id, or user_id'}

#         session = self.env['pos.session'].search([('id', '=', session_id), ('state', '=', 'opened')], limit=1)
#         if not session:
#             _logger.error("[SessionLock] Session %s does not exist or is not open", session_id)
#             return {'status': 'error', 'message': 'Session does not exist or is not open'}

#         current_lock = self.search([('session_id', '=', session_id), ('device_id', '=', device_id)], limit=1)
#         if current_lock:
#             _logger.info("[SessionLock] Updating existing lock for session %s, device %s", session_id, device_id)
#             current_lock.write({'is_active': True, 'lock_time': datetime.now(), 'user_id': user_id})
#         else:
#             _logger.info("[SessionLock] Creating new lock for session %s, device %s", session_id, device_id)
#             self.create({
#                 'session_id': session_id,
#                 'device_id': device_id,
#                 'user_id': user_id,
#                 'lock_time': datetime.now(),
#                 'is_active': True
#             })
#         active_count = self.get_active_device_count(session_id)
#         active_devices = self.get_active_devices(session_id)
#         _logger.info("[SessionLock] Lock created/updated for session %s, device %s, active devices: %d, device_list: %s", 
#                      session_id, device_id, active_count, active_devices)
#         return {'status': 'success', 'active_devices': active_count, 'device_list': active_devices}

#     @api.model
#     def check_device_in_session(self, session_id, device_id):
#         _logger.info("[SessionLock] Checking device %s in session %s", device_id, session_id)
#         lock = self.search([('session_id', '=', session_id), ('device_id', '=', device_id), ('is_active', '=', True)], limit=1)
#         if lock:
#             _logger.info("[SessionLock] Device %s is active in session %s, user: %s", device_id, session_id, lock.user_id.name)
#             return {'status': 'active', 'user_name': lock.user_id.name, 'is_active': True}
#         _logger.info("[SessionLock] Device %s is not active in session %s", device_id, session_id)
#         return {'status': 'inactive', 'is_active': False}

#     @api.model
#     def check_session_lock(self, session_id, device_id):
#         _logger.info("[SessionLock] Checking lock status for session %s, device %s", session_id, device_id)
#         locks = self.search([('session_id', '=', session_id), ('is_active', '=', True), ('device_id', '!=', device_id)])
#         if locks:
#             device_list = ", ".join([f"{lock.user_id.name} ({lock.device_id})" for lock in locks])
#             _logger.warning("[SessionLock] Session %s locked by %d other devices: %s", session_id, len(locks), device_list)
#             return {
#                 'status': 'locked',
#                 'device_list': [{'device_id': lock.device_id, 'user_name': lock.user_id.name, 'is_active': lock.is_active} for lock in locks]
#             }
#         _logger.info("[SessionLock] Session %s is free for device %s", session_id, device_id)
#         return {'status': 'free', 'device_list': []}

#     @api.model
#     def release_session_lock(self, session_id, device_id):
#         _logger.info("[SessionLock] Releasing lock for session %s, device %s", session_id, device_id)
#         lock = self.search([('session_id', '=', session_id), ('device_id', '=', device_id), ('is_active', '=', True)], limit=1)
#         if not lock:
#             _logger.info("[SessionLock] No active lock found for session %s, device %s", session_id, device_id)
#             return {'status': 'no_lock', 'active_devices': self.get_active_device_count(session_id)}
#         lock.write({'is_active': False, 'lock_time': datetime.now()})
#         active_count = self.get_active_device_count(session_id)
#         active_devices = self.get_active_devices(session_id)
#         _logger.info("[SessionLock] Lock released for session %s by device %s, active devices: %d, device_list: %s", 
#                      session_id, device_id, active_count, active_devices)
#         return {'status': 'success', 'active_devices': active_count, 'device_list': active_devices}

#     @api.model
#     def cleanup_stale_locks(self):
#         timeout = datetime.now() - timedelta(minutes=5)
#         stale_locks = self.search([('lock_time', '<', timeout), ('is_active', '=', True)])
#         if stale_locks:
#             _logger.info("[SessionLock] Cleaning %d stale locks", len(stale_locks))
#             stale_locks.write({'is_active': False})
#             for lock in stale_locks:
#                 _logger.info("[SessionLock] Deactivated stale lock for session %s, device %s", lock.session_id.id, lock.device_id)
#         return len(stale_locks)

#     @api.model
#     def get_active_device_count(self, session_id):
#         active_count = self.search_count([('session_id', '=', session_id), ('is_active', '=', True)])
#         _logger.debug("[SessionLock] Active device count for session %s: %d", session_id, active_count)
#         return active_count

#     @api.model
#     def get_active_devices(self, session_id):
#         locks = self.search([('session_id', '=', session_id), ('is_active', '=', True)])
#         devices = [{'device_id': lock.device_id, 'user_name': lock.user_id.name, 'is_active': lock.is_active} for lock in locks]
#         _logger.debug("[SessionLock] Active devices for session %s: %s", session_id, devices)
#         return devices

#     @api.model
#     def long_poll_session_status(self, session_id, device_id):
#         try:
#             _logger.info("[SessionLock] Polling session %s for device %s", session_id, device_id)
#             self.cleanup_stale_locks()
#             lock = self.search([('session_id', '=', session_id), ('device_id', '=', device_id), ('is_active', '=', True)], limit=1)
#             if lock:
#                 lock.write({'lock_time': datetime.now()})
#                 _logger.debug("[SessionLock] Updated lock_time for session %s, device %s", session_id, device_id)
            
#             active_count = self.get_active_device_count(session_id)
#             active_devices = self.get_active_devices(session_id)
#             _logger.info("[SessionLock] Polling result for session %s, device %s: %d active devices, device_list: %s", 
#                          session_id, device_id, active_count, active_devices)
            
#             if active_count <= 1:
#                 return {'status': 'free', 'active_devices': active_devices, 'device_count': active_count}
            
#             other_locks = self.search([('session_id', '=', session_id), ('is_active', '=', True), ('device_id', '!=', device_id)])
#             if other_locks:
#                 device_list = [{'device_id': lock.device_id, 'user_name': lock.user_id.name, 'is_active': lock.is_active} for lock in other_locks]
#                 _logger.warning("[SessionLock] Session %s locked by %d other devices: %s", session_id, len(other_locks), device_list)
#                 return {
#                     'status': 'locked',
#                     'device_list': device_list,
#                     'active_devices': active_devices,
#                     'device_count': active_count
#                 }
            
#             timeout = datetime.now() + timedelta(seconds=30)
#             while datetime.now() < timeout:
#                 self.env.cr.commit()
#                 active_count = self.get_active_device_count(session_id)
#                 active_devices = self.get_active_devices(session_id)
#                 lock = self.search([('session_id', '=', session_id), ('device_id', '=', device_id), ('is_active', '=', True)], limit=1)
#                 if active_count <= 1:
#                     _logger.info("[SessionLock] Polling found session %s free for device %s, active devices: %d", session_id, device_id, active_count)
#                     return {'status': 'free', 'active_devices': active_devices, 'device_count': active_count}
#                 other_locks = self.search([('session_id', '=', session_id), ('is_active', '=', True), ('device_id', '!=', device_id)])
#                 if other_locks:
#                     device_list = [{'device_id': lock.device_id, 'user_name': lock.user_id.name, 'is_active': lock.is_active} for lock in other_locks]
#                     _logger.warning("[SessionLock] Polling found session %s locked by %d other devices: %s", session_id, len(other_locks), device_list)
#                     return {
#                         'status': 'locked',
#                         'device_list': device_list,
#                         'active_devices': active_devices,
#                         'device_count': active_count
#                     }
#                 time.sleep(1)
#             _logger.warning("[SessionLock] Polling timeout for session %s, device %s, active devices: %d", session_id, device_id, active_count)
#             return {'status': 'timeout', 'active_devices': active_devices, 'device_count': active_count}
#         except Exception as e:
#             _logger.error("[SessionLock] Polling error for session %s, device %s: %s", session_id, device_id, str(e))
#             return {'status': 'error', 'message': str(e), 'active_devices': [], 'device_count': 0}





# from odoo import models, fields, api
# import logging
# from datetime import datetime, timedelta
# import time
# import threading

# _logger = logging.getLogger(__name__)

# _logger.info("[SessionLock] Initializing pos_session_lock module")

# class PosSessionLock(models.Model):
#     _name = "pos.session.lock"
#     _description = "POS Session Lock"

#     session_id = fields.Many2one('pos.session', string='Session', required=True, index=True,
#                                  domain="[('state', '=', 'opened')]")
#     device_id = fields.Char(string='Device ID', required=True, index=True)
#     is_active = fields.Boolean(string='Is Active', default=True)
#     lock_time = fields.Datetime(string='Lock Time', default=fields.Datetime.now)
#     user_id = fields.Many2one('res.users', string='User', required=True)

#     @api.model
#     def create_session_lock(self, session_id, device_id, user_id):
#         _logger.info("[SessionLock] Creating lock for session %s, device %s, user %s", session_id, device_id, user_id)
#         if not all([session_id, device_id, user_id]):
#             _logger.error("[SessionLock] Invalid parameters: session_id=%s, device_id=%s, user_id=%s", session_id, device_id, user_id)
#             return {'status': 'error', 'message': 'Missing session_id, device_id, or user_id'}

#         session = self.env['pos.session'].search([('id', '=', session_id), ('state', '=', 'opened')], limit=1)
#         if not session:
#             _logger.error("[SessionLock] Session %s does not exist or is not open", session_id)
#             return {'status': 'error', 'message': 'Session does not exist or is not open'}

#         current_lock = self.search([('session_id', '=', session_id), ('device_id', '=', device_id)], limit=1)
#         if current_lock:
#             _logger.info("[SessionLock] Updating existing lock for session %s, device %s", session_id, device_id)
#             current_lock.write({'is_active': True, 'lock_time': datetime.now(), 'user_id': user_id})
#         else:
#             _logger.info("[SessionLock] Creating new lock for session %s, device %s", session_id, device_id)
#             self.create({
#                 'session_id': session_id,
#                 'device_id': device_id,
#                 'user_id': user_id,
#                 'lock_time': datetime.now(),
#                 'is_active': True
#             })
#         active_count = self.get_active_device_count(session_id)
#         active_devices = self.get_active_devices(session_id)
#         _logger.info("[SessionLock] Lock created/updated for session %s, device %s, active devices: %d, device_list: %s", 
#                      session_id, device_id, active_count, active_devices)
#         return {'status': 'success', 'active_devices': active_count, 'device_list': active_devices}

#     @api.model
#     def check_device_in_session(self, session_id, device_id):
#         _logger.info("[SessionLock] Checking device %s in session %s", device_id, session_id)
#         lock = self.search([('session_id', '=', session_id), ('device_id', '=', device_id), ('is_active', '=', True)], limit=1)
#         if lock:
#             _logger.info("[SessionLock] Device %s is active in session %s, user: %s", device_id, session_id, lock.user_id.name)
#             return {'status': 'active', 'user_name': lock.user_id.name, 'is_active': True}
#         _logger.info("[SessionLock] Device %s is not active in session %s", device_id, session_id)
#         return {'status': 'inactive', 'is_active': False}

#     @api.model
#     def check_session_lock(self, session_id, device_id):
#         _logger.info("[SessionLock] Checking lock status for session %s, device %s", session_id, device_id)
#         locks = self.search([('session_id', '=', session_id), ('is_active', '=', True), ('device_id', '!=', device_id)])
#         if locks:
#             device_list = ", ".join([f"{lock.user_id.name} ({lock.device_id})" for lock in locks])
#             _logger.warning("[SessionLock] Session %s locked by %d other devices: %s", session_id, len(locks), device_list)
#             return {
#                 'status': 'locked',
#                 'device_list': [{'device_id': lock.device_id, 'user_name': lock.user_id.name, 'is_active': lock.is_active} for lock in locks]
#             }
#         _logger.info("[SessionLock] Session %s is free for device %s", session_id, device_id)
#         return {'status': 'free', 'device_list': []}

#     @api.model
#     def release_session_lock(self, session_id, device_id):
#         _logger.info("[SessionLock] Releasing lock for session %s, device %s", session_id, device_id)
#         lock = self.search([('session_id', '=', session_id), ('device_id', '=', device_id), ('is_active', '=', True)], limit=1)
#         if not lock:
#             _logger.info("[SessionLock] No active lock found for session %s, device %s", session_id, device_id)
#             return {'status': 'no_lock', 'active_devices': self.get_active_device_count(session_id)}
#         lock.write({'is_active': False, 'lock_time': datetime.now()})
#         active_count = self.get_active_device_count(session_id)
#         active_devices = self.get_active_devices(session_id)
#         _logger.info("[SessionLock] Lock released for session %s by device %s, active devices: %d, device_list: %s", 
#                      session_id, device_id, active_count, active_devices)
#         return {'status': 'success', 'active_devices': active_count, 'device_list': active_devices}

#     @api.model
#     def cleanup_stale_locks(self):
#         timeout = datetime.now() - timedelta(minutes=5)
#         stale_locks = self.search([('lock_time', '<', timeout), ('is_active', '=', True)])
#         if stale_locks:
#             _logger.info("[SessionLock] Cleaning %d stale locks", len(stale_locks))
#             stale_locks.write({'is_active': False})
#             for lock in stale_locks:
#                 _logger.info("[SessionLock] Deactivated stale lock for session %s, device %s", lock.session_id.id, lock.device_id)
#         return len(stale_locks)

#     @api.model
#     def get_active_device_count(self, session_id):
#         active_count = self.search_count([('session_id', '=', session_id), ('is_active', '=', True)])
#         _logger.debug("[SessionLock] Active device count for session %s: %d", session_id, active_count)
#         return active_count

#     @api.model
#     def get_active_devices(self, session_id):
#         locks = self.search([('session_id', '=', session_id), ('is_active', '=', True)])
#         devices = [{'device_id': lock.device_id, 'user_name': lock.user_id.name, 'is_active': lock.is_active} for lock in locks]
#         _logger.debug("[SessionLock] Active devices for session %s: %s", session_id, devices)
#         return devices

#     @api.model
#     def long_poll_session_status(self, session_id, device_id):
#         try:
#             _logger.info("[SessionLock] Polling session %s for device %s", session_id, device_id)
#             self.cleanup_stale_locks()
#             lock = self.search([('session_id', '=', session_id), ('device_id', '=', device_id), ('is_active', '=', True)], limit=1)
#             if lock:
#                 lock.write({'lock_time': datetime.now()})
#                 _logger.debug("[SessionLock] Updated lock_time for session %s, device %s", session_id, device_id)
            
#             active_count = self.get_active_device_count(session_id)
#             active_devices = self.get_active_devices(session_id)
#             _logger.info("[SessionLock] Polling result for session %s, device %s: %d active devices, device_list: %s", 
#                          session_id, device_id, active_count, active_devices)
            
#             if active_count <= 1:
#                 return {'status': 'free', 'active_devices': active_devices, 'device_count': active_count}
            
#             other_locks = self.search([('session_id', '=', session_id), ('is_active', '=', True), ('device_id', '!=', device_id)])
#             if other_locks:
#                 device_list = [{'device_id': lock.device_id, 'user_name': lock.user_id.name, 'is_active': lock.is_active} for lock in other_locks]
#                 _logger.warning("[SessionLock] Session %s locked by %d other devices: %s", session_id, len(other_locks), device_list)
#                 return {
#                     'status': 'locked',
#                     'device_list': device_list,
#                     'active_devices': active_devices,
#                     'device_count': active_count
#                 }
            
#             timeout = datetime.now() + timedelta(seconds=30)
#             while datetime.now() < timeout:
#                 self.env.cr.commit()
#                 active_count = self.get_active_device_count(session_id)
#                 active_devices = self.get_active_devices(session_id)
#                 lock = self.search([('session_id', '=', session_id), ('device_id', '=', device_id), ('is_active', '=', True)], limit=1)
#                 if active_count <= 1:
#                     _logger.info("[SessionLock] Polling found session %s free for device %s, active devices: %d", session_id, device_id, active_count)
#                     return {'status': 'free', 'active_devices': active_devices, 'device_count': active_count}
#                 other_locks = self.search([('session_id', '=', session_id), ('is_active', '=', True), ('device_id', '!=', device_id)])
#                 if other_locks:
#                     device_list = [{'device_id': lock.device_id, 'user_name': lock.user_id.name, 'is_active': lock.is_active} for lock in other_locks]
#                     _logger.warning("[SessionLock] Polling found session %s locked by %d other devices: %s", session_id, len(other_locks), device_list)
#                     return {
#                         'status': 'locked',
#                         'device_list': device_list,
#                         'active_devices': active_devices,
#                         'device_count': active_count
#                     }
#                 time.sleep(1)
#             _logger.warning("[SessionLock] Polling timeout for session %s, device %s, active devices: %d", session_id, device_id, active_count)
#             return {'status': 'timeout', 'active_devices': active_devices, 'device_count': active_count}
#         except Exception as e:
#             _logger.error("[SessionLock] Polling error for session %s, device %s: %s", session_id, device_id, str(e))
#             return {'status': 'error', 'message': str(e), 'active_devices': [], 'device_count': 0}

#     @api.model
#     def log_active_devices_periodically(self):
#         """Periodically log active devices for all open POS sessions."""
#         _logger.info("[SessionLock] Starting periodic active device logging")
#         while True:
#             try:
#                 open_sessions = self.env['pos.session'].search([('state', '=', 'opened')])
#                 for session in open_sessions:
#                     active_count = self.search_count([('session_id', '=', session.id), ('is_active', '=', True)])
#                     active_devices = self.get_active_devices(session.id)
#                     device_text = "device is active" if active_count == 1 else "devices are active"
#                     device_list = ", ".join([f"{d['user_name']} ({d['device_id']}, is_active={d['is_active']})" for d in active_devices]) or "None"
#                     _logger.info("[SessionLock] Session %s: %d %s at %s [%s]", session.id, active_count, device_text, datetime.now(), device_list)
#                 time.sleep(5)  # Wait 5 seconds before the next log
#             except Exception as e:
#                 _logger.error("[SessionLock] Error in periodic active device logging: %s", str(e))
#                 time.sleep(5)  # Continue after error to avoid crashing