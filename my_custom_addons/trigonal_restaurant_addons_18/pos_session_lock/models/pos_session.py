from odoo import models, fields
from odoo.exceptions import ValidationError
import logging

_logger = logging.getLogger(__name__)

class PosOrder(models.Model):
    _inherit = 'pos.order'

    device_id = fields.Char(string='Device ID', help="The ID of the device that created this order", readonly=True)

class PosSession(models.Model):
    _inherit = 'pos.session'

    def _get_active_device_count(self):
        """Return the count of active devices in the session."""
        return self.env['pos.session.lock'].search_count([
            ('session_id', '=', self.id),
            ('is_active', '=', True)
        ])

    def _validate_session(self, device_id=None, balancing_account=False, amount_to_balance=0, bank_payment_method_diffs=None):
        """Validate session before closing, checking for multiple active devices."""
        _logger.info("[SessionLock] Validating session %s for device %s", self.id, device_id)

        # Ensure device_id is provided
        if not device_id:
            device_id = self.env.context.get('device_id')
            if not device_id:
                raise ValidationError("Device ID is required to close the session.")

        # Check session state
        if self.state not in ['opened']:
            raise ValidationError("Cannot close the session: it is not in an open state.")

        # Check if multiple devices are active
        active_device_count = self._get_active_device_count()
        _logger.info("[SessionLock] Session %s has %d active devices", self.id, active_device_count)
        
        if active_device_count > 1:
            # Get details of other active devices
            other_locks = self.env['pos.session.lock'].search([
                ('session_id', '=', self.id),
                ('is_active', '=', True),
                ('device_id', '!=', device_id)
            ])
            
            if other_locks:
                device_list = [f"{lock.user_id.name} ({lock.device_id})" for lock in other_locks]
                raise ValidationError(
                    f"Cannot close the session: {len(other_locks)} other devices are currently active: {', '.join(device_list)}. "
                    "Please wait for other users to finish or ask them to close their POS sessions first."
                )

        # Check for active orders from other devices
        active_orders = self.env['pos.order'].search([
            ('session_id', '=', self.id),
            ('state', 'in', ['draft', 'paid'])
        ])
        
        if active_orders:
            other_device_orders = [order for order in active_orders if order.device_id and order.device_id != device_id]
            if other_device_orders:
                _logger.warning(
                    "[SessionLock] Found active orders from other devices in session %s: %s",
                    self.id, [(o.id, o.state, o.device_id) for o in other_device_orders]
                )
                raise ValidationError(
                    "Cannot close the session: there are active orders being processed on another device. "
                    "Please wait for them to complete before closing the session."
                )

        # Log all active orders for debugging
        if active_orders:
            _logger.info(
                "[SessionLock] Active orders in session %s: %s",
                self.id, [(o.id, o.state, o.device_id or 'No device') for o in active_orders]
            )

        # Proceed with base validation
        return super()._validate_session(balancing_account, amount_to_balance, bank_payment_method_diffs)

    def close_session_from_ui(self, device_id=None, bank_payment_method_diff_pairs=None):
        """Close session from UI with device validation."""
        _logger.info("[SessionLock] Attempting to close session %s, device_id: %s", self.id, device_id)
        
        # Get device_id from context if not provided as parameter
        device_id = device_id or self.env.context.get('device_id')
        if not device_id:
            _logger.error("[SessionLock] No device_id provided when closing session %s", self.id)
            return {
                'successful': False,
                'message': 'Device ID is required to close the session.'
            }

        # Check if this device can close the session
        lock_check = self.env['pos.session.lock'].can_close_session(self.id, device_id)
        if not lock_check['can_close']:
            _logger.warning("[SessionLock] Device %s cannot close session %s: %s", device_id, self.id, lock_check['message'])
            return {
                'successful': False,
                'message': lock_check['message']
            }

        # Validate the session with the device_id
        try:
            self._validate_session(device_id)
        except ValidationError as e:
            _logger.warning("[SessionLock] Validation failed for session %s, device %s: %s", self.id, device_id, str(e))
            return {
                'successful': False,
                'message': str(e)
            }

        # Proceed with the rest of the closing logic
        check_result = self._cannot_close_session(
            bank_payment_method_diffs=dict(bank_payment_method_diff_pairs or {})
        )
        if check_result:
            _logger.warning("[SessionLock] Cannot close session %s: %s", self.id, check_result.get('message'))
            return check_result

        try:
            validate_result = self.action_pos_session_closing_control(
                bank_payment_method_diffs=dict(bank_payment_method_diff_pairs or {})
            )
            if isinstance(validate_result, dict):
                _logger.warning("[SessionLock] Validation failed for session %s: %s", self.id, validate_result.get('name'))
                return {
                    'successful': False,
                    'message': validate_result.get('name'),
                    'redirect': True
                }
        except Exception as e:
            _logger.error("[SessionLock] Error during session %s closing control: %s", self.id, str(e))
            return {
                'successful': False,
                'message': str(e)
            }

        # Release the session lock for this device
        self.env['pos.session.lock'].release_session_lock(self.id, device_id)
        _logger.info("[SessionLock] Session %s closed successfully by device %s", self.id, device_id)
        self.post_close_register_message()
        return {'successful': True}

    def action_pos_session_open(self):
        """Override to log when session is opened."""
        _logger.info("[SessionLock] Opening session %s", self.id)
        result = super().action_pos_session_open()
        active_device_count = self._get_active_device_count()
        _logger.info("[SessionLock] Session %s opened with %d active devices", self.id, active_device_count)
        return result
