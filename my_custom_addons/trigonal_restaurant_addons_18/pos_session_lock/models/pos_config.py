
from odoo import api, fields, models
import logging

_logger = logging.getLogger(__name__)

class PosConfig(models.Model):
    _inherit = 'pos.config'

    last_session_id = fields.Many2one(
        'pos.session',
        string="Last Session",
        compute='_compute_last_session',
        store=False
    )
    last_session_closing_date = fields.Datetime(
        string="Last Session Closing Date",
        compute='_compute_last_session',
        store=False
    )
    last_session_closing_cash = fields.Float(
        string="Last Session Closing Cash",
        compute='_compute_last_session',
        store=False
    )

    def _compute_last_session(self):
        for config in self:
            last_session = self.env['pos.session'].search(
                [('config_id', '=', config.id), ('state', '=', 'closed')],
                order='stop_at desc',
                limit=1
            )
            if last_session:
                config.last_session_id = last_session
                # Assign naive datetime (no tzinfo) as required by Odoo
                stop_at = last_session.stop_at
                if stop_at and getattr(stop_at, 'tzinfo', None):
                    # If stop_at is timezone-aware, make it naive in UTC
                    stop_at = stop_at.astimezone(fields.Datetime.context_timestamp(config, stop_at).tzinfo).replace(tzinfo=None)
                config.last_session_closing_date = stop_at if stop_at else False
                config.last_session_closing_cash = last_session.cash_register_balance_end_real
            else:
                config.last_session_id = False
                config.last_session_closing_date = False
                config.last_session_closing_cash = 0.0
                _logger.info("[SessionLock] No valid last session found for config %s, setting defaults", config.id)
