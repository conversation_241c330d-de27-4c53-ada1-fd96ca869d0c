import logging

_logger = logging.getLogger(__name__)

def post_init_hook(env):
    """Initialize device IDs for existing POS orders."""
    _logger.info("[SessionLock] Running post_init_hook for pos_session_lock")
    try:
        records = env['pos.order'].search([('device_id', '=', False)])
        for record in records:
            record.write({'device_id': 'default_device_' + str(record.id)})
        _logger.info("[SessionLock] Updated %d POS orders with device IDs", len(records))
    except Exception as e:
        _logger.error("[SessionLock] Error in post_init_hook: %s", str(e))
