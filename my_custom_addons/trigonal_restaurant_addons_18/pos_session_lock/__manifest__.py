{
    'name': 'POS Session Lock',
    'version': '1.0',
    'category': 'Point of Sale',
    'summary': 'Prevent concurrent POS session closure with device-based locking',
    'depends': ['point_of_sale', 'pos_restaurant'],
    'data': [
        'security/ir.model.access.csv',
    ],
    'assets': {
        'point_of_sale._assets_pos': [
            'pos_session_lock/static/src/js/pos_session_lock.js',
        ],
    },
    'installable': True,
    'auto_install': False,
}
