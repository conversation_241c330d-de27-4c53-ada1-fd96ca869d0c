/** @odoo-module **/

import { patch } from "@web/core/utils/patch";
import { PosStore } from "@point_of_sale/app/store/pos_store";

console.log('[SessionLock] 🔥 POS SESSION LOCK PATCH LOADING...');

patch(PosStore.prototype, {
    async after_load_server_data() {
        console.log('[SessionLock] 🚀 after_load_server_data called - initializing session lock...');
        await super.after_load_server_data();

        // Initialize session lock properties
        this.sessionLockInterval = null;
        this.deviceId = this.getOrCreateDeviceId();
        this._lastActiveDevices = null;

        console.log('[SessionLock] 📱 Device ID initialized:', this.deviceId);

        // Initialize session lock and start polling
        await this.initializeSessionLock();
        this.startSessionLockPolling();
    },

    getOrCreateDeviceId() {
        // Try to get existing device ID from localStorage
        let deviceId = localStorage.getItem('pos_device_id');
        if (!deviceId) {
            // Generate a new unique device ID
            deviceId = 'device_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11);
            localStorage.setItem('pos_device_id', deviceId);
            console.log('[SessionLock] Generated new device ID:', deviceId);
        } else {
            console.log('[SessionLock] Retrieved existing device ID:', deviceId);
        }
        return deviceId;
    },

    async after_load_server_data() {
        await super.after_load_server_data();
        console.log('[SessionLock] After load server data, initializing session lock for device', this.deviceId);
        await this.initializeSessionLock();
        this.startSessionLockPolling();
    },

    async initializeSessionLock() {
        const session_id = this.session?.id || this.pos_session?.id;
        const user_id = this.cashier?.id || this.env.session.uid;
        
        if (!session_id || !user_id) {
            console.error('[SessionLock] Missing session or user ID', { session_id, user_id });
            this.notification?.add('Missing session or user ID.', { type: 'danger', sticky: true });
            return;
        }

        console.log('[SessionLock] Initializing lock for session', session_id, 'device', this.deviceId, 'user', user_id);
        
        try {
            const response = await this.data.call('pos.session.lock', 'create_session_lock', [session_id, this.deviceId, user_id]);
            console.log('[SessionLock] Initialize session lock response:', response);

            if (response.status === 'error') {
                this.notification?.add(response.message, { type: 'danger', sticky: true });
            } else {
                // Show detailed device information
                console.log('[SessionLock] 📱 SESSION DEVICE STATUS:');
                console.log('[SessionLock] 📊 Total active devices in session:', response.active_devices);

                if (response.device_list && response.device_list.length > 0) {
                    response.device_list.forEach((device, index) => {
                        const isCurrentDevice = device.device_id === this.deviceId;
                        const status = isCurrentDevice ? '👈 THIS DEVICE' : '🔗 OTHER DEVICE';
                        console.log(`[SessionLock] 📱 Device ${index + 1}: ${device.user_name} (${device.device_id}) ${status}`);
                    });
                } else {
                    console.log('[SessionLock] 📱 No other devices found in this session');
                }

                const deviceList = response.device_list?.map(d => `${d.user_name} (${d.device_id})`).join(', ') || 'None';
                this.notification?.add(
                    `Session lock initialized. Active devices: ${response.active_devices} [${deviceList}]`,
                    { type: 'info', sticky: false }
                );
            }
        } catch (error) {
            console.error('[SessionLock] Error initializing session lock:', error);
            this.notification?.add('Error initializing session lock.', { type: 'danger', sticky: false });
        }
    },

    startSessionLockPolling() {
        const session_id = this.session?.id || this.pos_session?.id;
        if (!session_id) {
            console.error('[SessionLock] No session ID for polling');
            this.notification?.add('Cannot start polling: missing session ID.', { type: 'danger', sticky: true });
            return;
        }

        console.log('[SessionLock] Starting polling for session', session_id, 'device', this.deviceId);
        this._lastActiveDevices = null;

        // Poll every 5 seconds
        this.sessionLockInterval = setInterval(async () => {
            try {
                // Send heartbeat and get current status
                const response = await this.data.call('pos.session.lock', 'heartbeat', [session_id, this.deviceId]);
                console.log('[SessionLock] Heartbeat response for session', session_id, 'device', this.deviceId, ':', response);
                
                if (response.status === 'active') {
                    const deviceList = response.device_list?.map(d => `${d.user_name} (${d.device_id})`).join(', ') || 'None';

                    // Only show notification if device list changed
                    if (JSON.stringify(this._lastActiveDevices) !== JSON.stringify(response.device_list)) {
                        console.log('[SessionLock] 📱 ACTIVE DEVICES UPDATE for session', session_id);
                        console.log('[SessionLock] 📊 Total active devices:', response.active_devices);

                        if (response.device_list && response.device_list.length > 0) {
                            response.device_list.forEach((device, index) => {
                                const isCurrentDevice = device.device_id === this.deviceId;
                                const status = isCurrentDevice ? '👈 THIS DEVICE' : '🔗 OTHER DEVICE';
                                console.log(`[SessionLock] 📱 Device ${index + 1}: ${device.user_name} (${device.device_id}) ${status}`);
                            });
                        }

                        this.notification?.add(
                            `🔗 Active devices in this session: ${response.active_devices} [${deviceList}]`,
                            { type: 'info', sticky: false, autocloseDelay: 3000 }
                        );
                        this._lastActiveDevices = response.device_list;
                    }
                } else if (response.status === 'inactive') {
                    console.warn('[SessionLock] Device', this.deviceId, 'is inactive in session', session_id);
                    // Try to re-register
                    await this.initializeSessionLock();
                }
            } catch (error) {
                console.error('[SessionLock] Polling error for session', session_id, 'device', this.deviceId, ':', error);
                this.notification?.add('Error polling session status.', { type: 'danger', sticky: false });
            }
        }, 5000); // Poll every 5 seconds
    },

    async closeSession() {
        const session_id = this.session?.id || this.pos_session?.id;
        if (!session_id) {
            console.error('[SessionLock] No session ID, proceeding with default close');
            return await super.closeSession();
        }

        console.log('[SessionLock] Attempting to close session', session_id, 'with device', this.deviceId);
        
        try {
            // Check if this device can close the session
            const canCloseResponse = await this.data.call('pos.session.lock', 'can_close_session', [session_id, this.deviceId]);
            console.log('[SessionLock] Can close session check:', canCloseResponse);
            
            if (!canCloseResponse.can_close) {
                console.warn('[SessionLock] Cannot close session:', canCloseResponse.message);
                this.notification?.add(canCloseResponse.message, {
                    type: 'warning',
                    sticky: true
                });
                return false;
            }

            // Proceed with session closure
            const result = await this.data.call(
                'pos.session',
                'close_session_from_ui',
                [[session_id]],
                { context: { device_id: this.deviceId } }
            );
            
            console.log('[SessionLock] Close session result:', result);
            
            if (!result.successful) {
                console.warn('[SessionLock] Failed to close session:', result.message);
                if (result.redirect) {
                    console.log('[SessionLock] Redirecting to POS session view due to closure failure');
                    window.location.href = '/web#action=point_of_sale.action_pos_session';
                } else {
                    this.notification?.add(result.message, {
                        type: 'danger',
                        sticky: true
                    });
                }
                return false;
            }

            console.log('[SessionLock] Session', session_id, 'closed successfully by device', this.deviceId);
            return await super.closeSession();
            
        } catch (error) {
            console.error('[SessionLock] Error closing session', session_id, 'device', this.deviceId, ':', error);
            const message = error.data?.message || error.message || 'Failed to close session.';
            this.notification?.add(message, {
                type: 'danger',
                sticky: true
            });
            return false;
        }
    },

    async closeSession() {
        console.log('[SessionLock] 🔒 SESSION CLOSURE ATTEMPT DETECTED!');

        const session_id = this.session?.id || this.pos_session?.id;
        if (!session_id) {
            console.log('[SessionLock] ⚠️ No session ID found, allowing closure');
            return await super.closeSession();
        }

        console.log('[SessionLock] 🔍 Checking if session can be closed for session:', session_id);

        try {
            const canClose = await this.canCloseSession();
            if (!canClose) {
                console.log('[SessionLock] ❌ SESSION CLOSURE BLOCKED - Other devices are active');
                this.notification?.add('Cannot close session: Other devices are still active in this session.', {
                    type: 'warning',
                    sticky: true
                });
                return false;
            }

            console.log('[SessionLock] ✅ SESSION CLOSURE ALLOWED - Proceeding with closure');
            return await super.closeSession();
        } catch (error) {
            console.error('[SessionLock] ❌ Error checking session closure:', error);
            // Allow closure on error to prevent blocking
            return await super.closeSession();
        }
    },

    willUnmount() {
        // Clear polling interval
        if (this.sessionLockInterval) {
            clearInterval(this.sessionLockInterval);
            this.sessionLockInterval = null;
            console.log('[SessionLock] Cleared polling interval for device', this.deviceId);
        }

        // Release session lock
        const session_id = this.session?.id || this.pos_session?.id;
        if (session_id && this.data) {
            console.log('[SessionLock] Releasing lock for session', session_id, 'device', this.deviceId);
            this.data.call('pos.session.lock', 'release_session_lock', [session_id, this.deviceId])
                .then(response => {
                    console.log('[SessionLock] Released lock for session', session_id, 'device', this.deviceId, 'response:', response);
                })
                .catch(error => {
                    console.error('[SessionLock] Error releasing lock for session', session_id, 'device', this.deviceId, ':', error);
                });
        }

        super.willUnmount?.();
    }
});
