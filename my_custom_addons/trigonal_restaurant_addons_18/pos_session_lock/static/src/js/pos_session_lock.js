/** @odoo-module **/

import { patch } from "@web/core/utils/patch";
import { PosStore } from "@point_of_sale/app/store/pos_store";

console.log('[SessionLock] 🔥 POS SESSION LOCK PATCH LOADING...');

patch(PosStore.prototype, {
    async set_cashier(employee) {
        console.log('[SessionLock] 👤 ===== CASHIER CHANGE EVENT =====');
        console.log('[SessionLock] 👤 Employee:', employee?.name, 'ID:', employee?.id);

        // Initialize session lock properties if not already done
        if (!this.sessionLockInitialized) {
            console.log('[SessionLock] 🚀 INITIALIZING SESSION LOCK...');
            this.sessionLockInterval = null;
            this.deviceId = this.getOrCreateDeviceId();
            this._lastActiveDevices = null;
            this.sessionLockInitialized = true;

            console.log('[SessionLock] 📱 Device ID initialized:', this.deviceId);

            // Initialize session lock and start polling with delay to ensure session is ready
            setTimeout(async () => {
                try {
                    await this.initializeSessionLock();
                    this.startSessionLockPolling();
                    this.setupUnloadListeners();
                } catch (error) {
                    console.error('[SessionLock] ❌ Error initializing session lock:', error);
                }
            }, 1000);
        }

        // Call the original set_cashier method
        return await super.set_cashier(employee);
    },

    getOrCreateDeviceId() {
        // Try to get existing device ID from localStorage
        let deviceId = localStorage.getItem('pos_device_id');
        if (!deviceId) {
            // Generate a new unique device ID
            deviceId = 'device_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11);
            localStorage.setItem('pos_device_id', deviceId);
            console.log('[SessionLock] Generated new device ID:', deviceId);
        } else {
            console.log('[SessionLock] Retrieved existing device ID:', deviceId);
        }
        return deviceId;
    },

    async initializeSessionLock() {
        try {
            const session_id = this.session?.id || this.pos_session?.id;
            // Try multiple ways to get user ID
            const user_id = this.cashier?.id || this.user?.id || this.env?.services?.user?.userId || 2; // fallback to admin user
        
        if (!session_id || !user_id) {
            console.error('[SessionLock] Missing session or user ID', { session_id, user_id });
            this.notification?.add('Missing session or user ID.', { type: 'danger', sticky: true });
            return;
        }

        console.log('[SessionLock] Initializing lock for session', session_id, 'device', this.deviceId, 'user', user_id);
        
        try {
            const response = await this.data.call('pos.session.lock', 'create_session_lock', [session_id, this.deviceId, user_id]);
            console.log('[SessionLock] Initialize session lock response:', response);

            if (response.status === 'error') {
                this.notification?.add(response.message, { type: 'danger', sticky: true });
            } else {
                // Show detailed device information
                console.log('[SessionLock] 📱 SESSION DEVICE STATUS:');
                console.log('[SessionLock] 📊 Total active devices in session:', response.active_devices);

                if (response.device_list && response.device_list.length > 0) {
                    response.device_list.forEach((device, index) => {
                        const isCurrentDevice = device.device_id === this.deviceId;
                        const status = isCurrentDevice ? '👈 THIS DEVICE' : '🔗 OTHER DEVICE';
                        console.log(`[SessionLock] 📱 Device ${index + 1}: ${device.user_name} (${device.device_id}) ${status}`);
                    });
                } else {
                    console.log('[SessionLock] 📱 No other devices found in this session');
                }

                const deviceList = response.device_list?.map(d => `${d.user_name} (${d.device_id})`).join(', ') || 'None';
                this.notification?.add(
                    `Session lock initialized. Active devices: ${response.active_devices} [${deviceList}]`,
                    { type: 'info', sticky: false }
                );
            }
        } catch (error) {
            console.error('[SessionLock] Error initializing session lock:', error);
            this.notification?.add('Error initializing session lock.', { type: 'danger', sticky: false });
        }
        } catch (error) {
            console.error('[SessionLock] ❌ Critical error in initializeSessionLock:', error);
        }
    },

    setupUnloadListeners() {
        console.log('[SessionLock] 🎯 Setting up unload listeners for immediate cleanup...');

        // Listen for page unload events
        const handleUnload = () => {
            console.log('[SessionLock] 🚪 Page unloading, releasing session lock...');
            // Use sendBeacon for immediate release on page unload
            const session_id = this.session?.id || this.pos_session?.id;
            if (session_id && this.deviceId) {
                const data = JSON.stringify({
                    jsonrpc: "2.0",
                    method: "call",
                    params: {
                        service: "object",
                        method: "call",
                        args: ["pos.session.lock", "release_session_lock", [session_id, this.deviceId]]
                    }
                });

                // Use sendBeacon for reliable delivery even when page is closing
                if (navigator.sendBeacon) {
                    navigator.sendBeacon('/web/dataset/call_kw', data);
                    console.log('[SessionLock] ✅ Sent beacon to release lock');
                }
            }
        };

        // Add event listeners
        window.addEventListener('beforeunload', handleUnload);
        window.addEventListener('unload', handleUnload);

        // Store reference to remove listeners later
        this._unloadHandler = handleUnload;
    },

    startSessionLockPolling() {
        const session_id = this.session?.id || this.pos_session?.id;
        if (!session_id) {
            console.error('[SessionLock] No session ID for polling');
            this.notification?.add('Cannot start polling: missing session ID.', { type: 'danger', sticky: true });
            return;
        }

        console.log('[SessionLock] Starting polling for session', session_id, 'device', this.deviceId);
        this._lastActiveDevices = null;

        // Poll every 5 seconds
        this.sessionLockInterval = setInterval(async () => {
            try {
                // Send heartbeat and get current status
                const response = await this.data.call('pos.session.lock', 'heartbeat', [session_id, this.deviceId]);
                console.log('[SessionLock] Heartbeat response for session', session_id, 'device', this.deviceId, ':', response);
                
                if (response.status === 'active') {
                    const deviceList = response.device_list?.map(d => `${d.user_name} (${d.device_id})`).join(', ') || 'None';

                    // Only show notification if device list changed
                    if (JSON.stringify(this._lastActiveDevices) !== JSON.stringify(response.device_list)) {
                        console.log('[SessionLock] 📱 ACTIVE DEVICES UPDATE for session', session_id);
                        console.log('[SessionLock] 📊 Total active devices:', response.active_devices);

                        if (response.device_list && response.device_list.length > 0) {
                            response.device_list.forEach((device, index) => {
                                const isCurrentDevice = device.device_id === this.deviceId;
                                const status = isCurrentDevice ? '👈 THIS DEVICE' : '🔗 OTHER DEVICE';
                                console.log(`[SessionLock] 📱 Device ${index + 1}: ${device.user_name} (${device.device_id}) ${status}`);
                            });
                        }

                        this.notification?.add(
                            `🔗 Active devices in this session: ${response.active_devices} [${deviceList}]`,
                            { type: 'info', sticky: false, autocloseDelay: 3000 }
                        );
                        this._lastActiveDevices = response.device_list;
                    }
                } else if (response.status === 'inactive') {
                    console.warn('[SessionLock] Device', this.deviceId, 'is inactive in session', session_id);
                    // Try to re-register
                    await this.initializeSessionLock();
                }
            } catch (error) {
                console.error('[SessionLock] Polling error for session', session_id, 'device', this.deviceId, ':', error);
                this.notification?.add('Error polling session status.', { type: 'danger', sticky: false });
            }
        }, 3000); // Poll every 3 seconds for faster updates
    },

    async closeSession() {
        console.log('[SessionLock] 🔒 SESSION CLOSURE ATTEMPT DETECTED!');

        const session_id = this.session?.id || this.pos_session?.id;
        if (!session_id) {
            console.log('[SessionLock] ⚠️ No session ID found, allowing closure');
            return await super.closeSession();
        }

        // Ensure device ID is available
        if (!this.deviceId) {
            this.deviceId = this.getOrCreateDeviceId();
        }

        console.log('[SessionLock] 🔍 Checking if session can be closed for session:', session_id, 'device:', this.deviceId);
        
        try {
            // Check if this device can close the session
            const canCloseResponse = await this.data.call('pos.session.lock', 'can_close_session', [session_id, this.deviceId]);
            console.log('[SessionLock] Can close session check:', canCloseResponse);
            
            if (!canCloseResponse.can_close) {
                console.warn('[SessionLock] Cannot close session:', canCloseResponse.message);
                this.notification?.add(canCloseResponse.message, {
                    type: 'warning',
                    sticky: true
                });
                return false;
            }

            // Proceed with session closure
            const result = await this.data.call(
                'pos.session',
                'close_session_from_ui',
                [[session_id]],
                { context: { device_id: this.deviceId } }
            );
            
            console.log('[SessionLock] Close session result:', result);
            
            if (!result.successful) {
                console.warn('[SessionLock] Failed to close session:', result.message);
                if (result.redirect) {
                    console.log('[SessionLock] Redirecting to POS session view due to closure failure');
                    window.location.href = '/web#action=point_of_sale.action_pos_session';
                } else {
                    this.notification?.add(result.message, {
                        type: 'danger',
                        sticky: true
                    });
                }
                return false;
            }

            console.log('[SessionLock] Session', session_id, 'closed successfully by device', this.deviceId);
            return await super.closeSession();
            
        } catch (error) {
            console.error('[SessionLock] Error closing session', session_id, 'device', this.deviceId, ':', error);
            const message = error.data?.message || error.message || 'Failed to close session.';
            this.notification?.add(message, {
                type: 'danger',
                sticky: true
            });
            return false;
        }
    },

    willUnmount() {
        console.log('[SessionLock] 🔄 POS Store unmounting, cleaning up...');

        // Remove unload listeners
        if (this._unloadHandler) {
            window.removeEventListener('beforeunload', this._unloadHandler);
            window.removeEventListener('unload', this._unloadHandler);
            console.log('[SessionLock] ✅ Removed unload listeners');
        }

        this.releaseSessionLock();
        super.willUnmount?.();
    },

    async releaseSessionLock() {
        console.log('[SessionLock] 🚪 Releasing session lock immediately...');

        // Clear polling interval
        if (this.sessionLockInterval) {
            clearInterval(this.sessionLockInterval);
            this.sessionLockInterval = null;
            console.log('[SessionLock] ✅ Cleared polling interval for device', this.deviceId);
        }

        // Release session lock immediately
        const session_id = this.session?.id || this.pos_session?.id;
        if (session_id && this.deviceId && this.data) {
            try {
                console.log('[SessionLock] 🔓 Releasing lock for session', session_id, 'device', this.deviceId);
                const response = await this.data.call('pos.session.lock', 'release_session_lock', [session_id, this.deviceId]);
                console.log('[SessionLock] ✅ Released lock successfully:', response);

                // Immediately update other devices about the change
                if (response && response.active_devices !== undefined) {
                    console.log('[SessionLock] 📊 Updated active devices count:', response.active_devices);
                }
            } catch (error) {
                console.error('[SessionLock] ❌ Error releasing lock:', error);
            }
        }
    },

    // Test method to verify JavaScript is working
    testSessionLock() {
        console.log('[SessionLock] 🧪 TEST METHOD CALLED!');
        console.log('[SessionLock] 🧪 Device ID:', this.deviceId);
        console.log('[SessionLock] 🧪 Session ID:', this.session?.id || this.pos_session?.id);
        console.log('[SessionLock] 🧪 User ID:', this.cashier?.id || this.user?.id || this.env?.services?.user?.userId);
        return 'SessionLock is working!';
    }
});
