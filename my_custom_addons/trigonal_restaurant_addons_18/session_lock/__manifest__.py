{
    'name': 'Session Lock',
    'version': '********.0',
    'summary': 'Locks the session and prevents closing the same session from another browser tab',
    'description': '''
        This module allows:
        - Prevent closing the same session from another tab
        - Warns the user the same session is running on another tab
        - Notes the session ID of one session and another so that only the same user can close the session but another session user cannot
    ''',
    'author': 'Trigonal Technology Pvt Ltd.',
    'website': 'https://www.trigonaltechnology.com',
    'depends': [
        'point_of_sale',
        'pos_restaurant',
        'hr',
    ],
    'data': [
        # (e.g., 'security/ir.model.access.csv')
    ],
    'assets': {
        'point_of_sale._assets_pos': [
            'session_lock/static/src/js/session_store.js',
            # 'session_lock/static/src/css/session_lock.css', 
            'session_lock/static/src/js/session_screen.js',
            'session_lock/static/src/xml/session_lock_templates.xml',
        ],
    },
    'installable': True,
    'auto_install': False,
    'application': False,
}