#!/usr/bin/env python3
"""
Test script to verify pos_session_lock backend methods are accessible.
"""

import xmlrpc.client
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Odoo connection details
url = 'http://localhost:8069'
db = 'admin'
username = 'admin'
password = 'admin'

def test_session_lock_methods():
    """Test if pos.session.lock methods are accessible via XML-RPC."""
    
    try:
        # Connect to Odoo
        common = xmlrpc.client.ServerProxy(f'{url}/xmlrpc/2/common')
        uid = common.authenticate(db, username, password, {})
        
        if not uid:
            logger.error("Authentication failed")
            return False
            
        logger.info(f"Authenticated as user ID: {uid}")
        
        # Get models proxy
        models = xmlrpc.client.ServerProxy(f'{url}/xmlrpc/2/object')
        
        # Test 1: Check if pos.session.lock model exists
        try:
            result = models.execute_kw(db, uid, password, 'pos.session.lock', 'search', [[]], {'limit': 1})
            logger.info(f"✅ pos.session.lock model exists. Search result: {result}")
        except Exception as e:
            logger.error(f"❌ pos.session.lock model not accessible: {e}")
            return False
        
        # Test 2: Check if we can call create_session_lock method
        try:
            # First get an open session
            sessions = models.execute_kw(db, uid, password, 'pos.session', 'search', [
                [('state', '=', 'opened')]
            ], {'limit': 1})
            
            if not sessions:
                logger.warning("⚠️ No open POS sessions found. Creating test data...")
                # Try to find a POS config
                configs = models.execute_kw(db, uid, password, 'pos.config', 'search', [[]], {'limit': 1})
                if configs:
                    # Create a session for testing
                    session_id = models.execute_kw(db, uid, password, 'pos.session', 'create', [{
                        'config_id': configs[0],
                        'user_id': uid,
                    }])
                    # Open the session
                    models.execute_kw(db, uid, password, 'pos.session', 'action_pos_session_open', [[session_id]])
                    sessions = [session_id]
                    logger.info(f"Created and opened test session: {session_id}")
                else:
                    logger.error("❌ No POS config found to create test session")
                    return False
            
            session_id = sessions[0]
            test_device_id = "test-device-123"
            
            # Test create_session_lock method
            result = models.execute_kw(db, uid, password, 'pos.session.lock', 'create_session_lock', [
                session_id, test_device_id, uid
            ])
            logger.info(f"✅ create_session_lock method works. Result: {result}")
            
            # Test heartbeat method
            result = models.execute_kw(db, uid, password, 'pos.session.lock', 'heartbeat', [
                session_id, test_device_id
            ])
            logger.info(f"✅ heartbeat method works. Result: {result}")
            
            # Test get_active_device_count method
            result = models.execute_kw(db, uid, password, 'pos.session.lock', 'get_active_device_count', [
                session_id
            ])
            logger.info(f"✅ get_active_device_count method works. Result: {result}")
            
            # Test can_close_session method
            result = models.execute_kw(db, uid, password, 'pos.session.lock', 'can_close_session', [
                session_id, test_device_id
            ])
            logger.info(f"✅ can_close_session method works. Result: {result}")
            
            # Clean up - release the lock
            models.execute_kw(db, uid, password, 'pos.session.lock', 'release_session_lock', [
                session_id, test_device_id
            ])
            logger.info("✅ Cleanup completed - lock released")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Error testing methods: {e}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Connection error: {e}")
        return False

if __name__ == "__main__":
    logger.info("Testing pos_session_lock backend methods...")
    success = test_session_lock_methods()
    if success:
        logger.info("🎉 All tests passed! Backend methods are accessible.")
    else:
        logger.error("💥 Tests failed! Backend methods are not accessible.")
