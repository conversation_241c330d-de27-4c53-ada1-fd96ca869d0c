#!/usr/bin/env python3
"""
Script to restart module and test session lock functionality
"""

import xmlrpc.client
import logging
import time

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Odoo connection details
url = 'http://localhost:8069'
db = 'admin'
username = 'admin'
password = 'admin'

def restart_and_test():
    """Restart module and test functionality."""
    
    try:
        # Connect to Odoo
        common = xmlrpc.client.ServerProxy(f'{url}/xmlrpc/2/common')
        uid = common.authenticate(db, username, password, {})
        
        if not uid:
            logger.error("Authentication failed")
            return False
            
        logger.info(f"Authenticated as user ID: {uid}")
        
        # Get models proxy
        models = xmlrpc.client.ServerProxy(f'{url}/xmlrpc/2/object')
        
        # Step 1: Try to restart the module
        try:
            module_ids = models.execute_kw(db, uid, password, 'ir.module.module', 'search', [
                [('name', '=', 'pos_session_lock')]
            ])
            
            if module_ids:
                module_id = module_ids[0]
                logger.info(f"🔄 Restarting pos_session_lock module (ID: {module_id})")
                
                # First uninstall
                models.execute_kw(db, uid, password, 'ir.module.module', 'button_immediate_uninstall', [[module_id]])
                logger.info("📦 Module uninstalled")
                time.sleep(2)
                
                # Then install
                models.execute_kw(db, uid, password, 'ir.module.module', 'button_immediate_install', [[module_id]])
                logger.info("📦 Module installed")
                time.sleep(3)
                
        except Exception as e:
            logger.warning(f"⚠️ Module restart failed (might be due to scheduled actions): {e}")
            logger.info("🔄 Trying to continue with current installation...")
        
        # Step 2: Check if model exists now
        model_exists = models.execute_kw(db, uid, password, 'ir.model', 'search_count', [
            [('model', '=', 'pos.session.lock')]
        ])
        
        if model_exists == 0:
            logger.error("❌ pos.session.lock model still doesn't exist")
            return False
            
        logger.info("✅ pos.session.lock model exists!")
        
        # Step 3: Test basic access
        try:
            result = models.execute_kw(db, uid, password, 'pos.session.lock', 'search', [[]], {'limit': 1})
            logger.info(f"✅ Basic access works! Found records: {result}")
        except Exception as e:
            logger.error(f"❌ Basic access failed: {e}")
            return False
        
        # Step 4: Test with a real session
        # Find an open POS session
        sessions = models.execute_kw(db, uid, password, 'pos.session', 'search_read', [
            [('state', '=', 'opened')], ['id', 'name', 'user_id']
        ], {'limit': 1})
        
        if not sessions:
            logger.warning("⚠️ No open POS sessions found. Creating test data...")
            # We'll test with session ID 1 anyway
            test_session_id = 1
        else:
            test_session_id = sessions[0]['id']
            logger.info(f"📍 Using session {test_session_id}: {sessions[0]['name']}")
        
        # Step 5: Test device registration
        test_device_id = "test_device_12345"
        test_user_id = uid
        
        logger.info(f"🧪 Testing device registration...")
        try:
            result = models.execute_kw(db, uid, password, 'pos.session.lock', 'create_session_lock', [
                test_session_id, test_device_id, test_user_id
            ])
            logger.info(f"✅ Device registration result: {result}")
        except Exception as e:
            logger.error(f"❌ Device registration failed: {e}")
            return False
        
        # Step 6: Test heartbeat
        logger.info(f"🧪 Testing heartbeat...")
        try:
            result = models.execute_kw(db, uid, password, 'pos.session.lock', 'heartbeat', [
                test_session_id, test_device_id
            ])
            logger.info(f"✅ Heartbeat result: {result}")
        except Exception as e:
            logger.error(f"❌ Heartbeat failed: {e}")
            return False
        
        # Step 7: Test session closure check
        logger.info(f"🧪 Testing session closure check...")
        try:
            result = models.execute_kw(db, uid, password, 'pos.session.lock', 'can_close_session', [
                test_session_id, test_device_id
            ])
            logger.info(f"✅ Session closure check result: {result}")
        except Exception as e:
            logger.error(f"❌ Session closure check failed: {e}")
            return False
        
        # Step 8: Test with second device to see multi-device behavior
        test_device_id_2 = "test_device_67890"
        
        logger.info(f"🧪 Testing second device registration...")
        try:
            result = models.execute_kw(db, uid, password, 'pos.session.lock', 'create_session_lock', [
                test_session_id, test_device_id_2, test_user_id
            ])
            logger.info(f"✅ Second device registration result: {result}")
        except Exception as e:
            logger.error(f"❌ Second device registration failed: {e}")
            return False
        
        # Step 9: Test session closure with multiple devices
        logger.info(f"🧪 Testing session closure with multiple devices...")
        try:
            result = models.execute_kw(db, uid, password, 'pos.session.lock', 'can_close_session', [
                test_session_id, test_device_id
            ])
            logger.info(f"✅ Multi-device closure check result: {result}")
            
            if result.get('can_close'):
                logger.warning("⚠️ Session closure was allowed with multiple devices - this might be unexpected")
            else:
                logger.info("✅ Session closure correctly blocked with multiple devices")
                
        except Exception as e:
            logger.error(f"❌ Multi-device closure check failed: {e}")
            return False
        
        # Step 10: Clean up test data
        logger.info(f"🧹 Cleaning up test data...")
        try:
            test_locks = models.execute_kw(db, uid, password, 'pos.session.lock', 'search', [
                [('device_id', 'in', [test_device_id, test_device_id_2])]
            ])
            if test_locks:
                models.execute_kw(db, uid, password, 'pos.session.lock', 'unlink', [test_locks])
                logger.info(f"✅ Cleaned up {len(test_locks)} test records")
        except Exception as e:
            logger.warning(f"⚠️ Cleanup failed: {e}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Error in restart and test: {e}")
        return False

if __name__ == "__main__":
    logger.info("🚀 Starting module restart and test...")
    
    if restart_and_test():
        logger.info("🎉 All tests passed!")
        logger.info("📝 The pos_session_lock module is working correctly.")
        logger.info("📝 Check the Odoo logs for detailed device tracking information.")
    else:
        logger.error("💥 Tests failed!")
