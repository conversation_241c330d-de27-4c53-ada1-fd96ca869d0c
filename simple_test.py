#!/usr/bin/env python3
"""
Simple test script for pos_session_lock
"""

import xmlrpc.client
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Odoo connection details
url = 'http://localhost:8069'
db = 'admin'
username = 'admin'
password = 'admin'

def simple_test():
    """Simple test of pos_session_lock functionality."""
    
    try:
        # Connect to Odoo
        common = xmlrpc.client.ServerProxy(f'{url}/xmlrpc/2/common')
        uid = common.authenticate(db, username, password, {})
        
        if not uid:
            logger.error("Authentication failed")
            return False
            
        logger.info(f"Authenticated as user ID: {uid}")
        
        # Get models proxy
        models = xmlrpc.client.ServerProxy(f'{url}/xmlrpc/2/object')
        
        # Check if model exists
        model_exists = models.execute_kw(db, uid, password, 'ir.model', 'search_count', [
            [('model', '=', 'pos.session.lock')]
        ])
        
        if model_exists == 0:
            logger.error("❌ pos.session.lock model doesn't exist")
            return False
            
        logger.info("✅ pos.session.lock model exists!")
        
        # Test basic access
        try:
            result = models.execute_kw(db, uid, password, 'pos.session.lock', 'search', [[]], {'limit': 1})
            logger.info(f"✅ Basic access works! Found records: {result}")
        except Exception as e:
            logger.error(f"❌ Basic access failed: {e}")
            return False
        
        # Test method calls
        try:
            # Test get_active_device_count
            count = models.execute_kw(db, uid, password, 'pos.session.lock', 'get_active_device_count', [1])
            logger.info(f"✅ get_active_device_count works: {count}")
        except Exception as e:
            logger.error(f"❌ get_active_device_count failed: {e}")
            return False
        
        # Test can_close_session
        try:
            result = models.execute_kw(db, uid, password, 'pos.session.lock', 'can_close_session', [1, 'test_device'])
            logger.info(f"✅ can_close_session works: {result}")
        except Exception as e:
            logger.error(f"❌ can_close_session failed: {e}")
            return False
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Error in simple test: {e}")
        return False

if __name__ == "__main__":
    logger.info("🧪 Running simple test...")
    
    if simple_test():
        logger.info("🎉 Simple test passed!")
        logger.info("📝 The pos_session_lock module is accessible.")
    else:
        logger.error("💥 Simple test failed!")
