#!/usr/bin/env python3

import xmlrpc.client
import time

# Odoo connection details
url = 'http://localhost:8069'
db = 'admin'
username = 'admin'
password = 'admin'

def test_device_listing():
    print("🔧 Testing Device Listing in Session Lock...")
    
    try:
        # Connect to Odoo
        common = xmlrpc.client.ServerProxy(f'{url}/xmlrpc/2/common')
        uid = common.authenticate(db, username, password, {})
        
        if not uid:
            print("❌ Authentication failed!")
            return False
            
        print(f"✅ Connected to Odoo as user {uid}")
        
        models = xmlrpc.client.ServerProxy(f'{url}/xmlrpc/2/object')
        
        # Find an open POS session
        sessions = models.execute_kw(db, uid, password, 'pos.session', 'search_read', 
                                   [[('state', '=', 'opened')]], {'fields': ['id', 'name'], 'limit': 1})
        
        if not sessions:
            print("❌ No open POS sessions found. Please open a POS session first.")
            return False
            
        session = sessions[0]
        session_id = session['id']
        print(f"✅ Found open session: {session['name']} (ID: {session_id})")
        
        # Simulate multiple devices joining the session
        devices = [
            {'id': f"device_test_1_{int(time.time())}", 'user': 'Device 1 User'},
            {'id': f"device_test_2_{int(time.time())}", 'user': 'Device 2 User'},
            {'id': f"device_test_3_{int(time.time())}", 'user': 'Device 3 User'}
        ]
        
        print(f"\n📱 Simulating {len(devices)} devices joining session {session_id}...")
        
        # Add devices one by one and show the device list each time
        for i, device in enumerate(devices):
            print(f"\n--- Adding Device {i+1}: {device['id']} ---")
            
            # Create session lock for this device
            result = models.execute_kw(db, uid, password, 'pos.session.lock', 'create_session_lock', 
                                     [session_id, device['id'], uid])
            
            print(f"✅ Device {i+1} added. Response: {result}")
            
            # Show current device list
            if result.get('device_list'):
                print(f"📊 Total active devices: {result['active_devices']}")
                for j, dev in enumerate(result['device_list']):
                    print(f"   📱 Device {j+1}: {dev['user_name']} ({dev['device_id']})")
            
            # Send heartbeat to keep device active
            models.execute_kw(db, uid, password, 'pos.session.lock', 'heartbeat', 
                            [session_id, device['id']])
        
        print(f"\n🔍 Final device count check...")
        final_count = models.execute_kw(db, uid, password, 'pos.session.lock', 'get_active_device_count', 
                                      [session_id])
        final_devices = models.execute_kw(db, uid, password, 'pos.session.lock', 'get_active_devices', 
                                        [session_id])
        
        print(f"📊 Final active device count: {final_count}")
        print(f"📱 Final device list:")
        for i, dev in enumerate(final_devices):
            print(f"   📱 Device {i+1}: {dev['user_name']} ({dev['device_id']})")
        
        # Test session closure with multiple devices
        print(f"\n🔒 Testing session closure with multiple devices...")
        can_close = models.execute_kw(db, uid, password, 'pos.session.lock', 'can_close_session', 
                                    [session_id, devices[0]['id']])
        print(f"Can close session with device 1: {can_close}")
        
        # Clean up - release all locks
        print(f"\n🧹 Cleaning up test devices...")
        for device in devices:
            models.execute_kw(db, uid, password, 'pos.session.lock', 'release_session_lock', 
                            [session_id, device['id']])
            print(f"✅ Released lock for {device['id']}")
        
        print("\n🎉 Device listing test completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        return False

if __name__ == "__main__":
    test_device_listing()
