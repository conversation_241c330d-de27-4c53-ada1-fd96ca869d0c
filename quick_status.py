#!/usr/bin/env python3
"""
Quick status check for pos_session_lock
"""

import xmlrpc.client
import logging
import socket

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Odoo connection details
url = 'http://localhost:8069'
db = 'admin'
username = 'admin'
password = 'admin'

def quick_status():
    """Quick status check."""
    
    try:
        # Test connection first
        logger.info("🔌 Testing connection to Odoo...")
        
        # Set a shorter timeout
        socket.setdefaulttimeout(10)
        
        common = xmlrpc.client.ServerProxy(f'{url}/xmlrpc/2/common')
        
        # Test basic connection
        try:
            version = common.version()
            logger.info(f"✅ Odoo connection OK - Version: {version}")
        except Exception as e:
            logger.error(f"❌ Connection failed: {e}")
            return False
        
        # Test authentication
        try:
            uid = common.authenticate(db, username, password, {})
            if not uid:
                logger.error("❌ Authentication failed")
                return False
            logger.info(f"✅ Authentication OK - User ID: {uid}")
        except Exception as e:
            logger.error(f"❌ Authentication error: {e}")
            return False
        
        # Test model access
        models = xmlrpc.client.ServerProxy(f'{url}/xmlrpc/2/object')
        
        try:
            # Check if model exists
            model_count = models.execute_kw(db, uid, password, 'ir.model', 'search_count', [
                [('model', '=', 'pos.session.lock')]
            ])
            
            if model_count > 0:
                logger.info("✅ pos.session.lock model exists")
                
                # Try to access the model
                try:
                    records = models.execute_kw(db, uid, password, 'pos.session.lock', 'search', [[]], {'limit': 1})
                    logger.info(f"✅ Model access OK - Found {len(records)} records")
                    
                    # Try a method call
                    try:
                        result = models.execute_kw(db, uid, password, 'pos.session.lock', 'get_active_device_count', [1])
                        logger.info(f"✅ Method calls work - Active devices: {result}")
                        return True
                    except Exception as e:
                        logger.error(f"❌ Method call failed: {e}")
                        return False
                        
                except Exception as e:
                    logger.error(f"❌ Model access failed: {e}")
                    return False
            else:
                logger.error("❌ pos.session.lock model doesn't exist")
                return False
                
        except Exception as e:
            logger.error(f"❌ Model check failed: {e}")
            return False
        
    except Exception as e:
        logger.error(f"❌ General error: {e}")
        return False

if __name__ == "__main__":
    logger.info("⚡ Quick status check...")
    
    if quick_status():
        logger.info("🎉 pos_session_lock module is working!")
        logger.info("📝 You can now test the POS session lock functionality.")
        logger.info("📝 The issues you mentioned should be fixed:")
        logger.info("   - Session closure will only be blocked when multiple devices are active")
        logger.info("   - Device information will be logged when sessions are opened")
    else:
        logger.error("💥 Module is not working properly!")
